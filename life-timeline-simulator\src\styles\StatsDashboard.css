/* StatsDashboard component styles */

.stats-dashboard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.stats-dashboard.compact {
  padding: 16px;
  margin-bottom: 16px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.4em;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.overall-score {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.score-label {
  font-size: 0.9em;
  color: #666;
  font-weight: 500;
}

.score-value {
  font-size: 1.2em;
  font-weight: 700;
  color: #2c3e50;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stats-dashboard.compact .stats-grid {
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.stats-summary {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.summary-label {
  font-size: 0.8em;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-value {
  font-size: 0.95em;
  font-weight: 600;
  color: #2c3e50;
}

/* Animation for stats grid */
.stats-grid {
  animation: statsGridFadeIn 0.8s ease-out;
}

@keyframes statsGridFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .stats-dashboard {
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .stats-dashboard.compact {
    padding: 12px;
    margin-bottom: 12px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
    margin-bottom: 16px;
  }
  
  .dashboard-header h3 {
    font-size: 1.2em;
  }
  
  .overall-score {
    align-self: stretch;
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .stats-summary {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }
  
  .summary-item {
    flex-direction: row;
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .stats-dashboard {
    padding: 16px;
    border-radius: 12px;
  }
  
  .dashboard-header h3 {
    font-size: 1.1em;
  }
  
  .overall-score {
    padding: 6px 12px;
  }
  
  .score-value {
    font-size: 1.1em;
  }
  
  .stats-grid {
    gap: 8px;
  }
  
  .stats-summary {
    padding: 10px;
  }
  
  .summary-label {
    font-size: 0.75em;
  }
  
  .summary-value {
    font-size: 0.9em;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .stats-dashboard {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .dashboard-header h3 {
    color: #ecf0f1;
  }
  
  .score-label {
    color: #bdc3c7;
  }
  
  .score-value {
    color: #ecf0f1;
  }
  
  .summary-label {
    color: #bdc3c7;
  }
  
  .summary-value {
    color: #ecf0f1;
  }
  
  .stats-summary {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
}
