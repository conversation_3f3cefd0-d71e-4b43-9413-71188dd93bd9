// Main stats dashboard component

import React, { useState, useEffect } from 'react';
import StatBar from './StatBar';
import { STATS } from '../data/gameData';
import { calculateLifeScore } from '../utils/gameUtils';
import '../styles/StatsDashboard.css';

const StatsDashboard = ({ 
  stats, 
  previousStats = null, 
  showChanges = false,
  compact = false 
}) => {
  const [animationKey, setAnimationKey] = useState(0);
  
  // Trigger animation when stats change
  useEffect(() => {
    if (previousStats) {
      setAnimationKey(prev => prev + 1);
    }
  }, [stats, previousStats]);

  const overallScore = calculateLifeScore(stats);
  
  return (
    <div className={`stats-dashboard ${compact ? 'compact' : ''}`}>
      <div className="dashboard-header">
        <h3>Life Stats</h3>
        <div className="overall-score">
          <span className="score-label">Overall:</span>
          <span className="score-value">{overallScore}</span>
        </div>
      </div>
      
      <div className="stats-grid" key={animationKey}>
        {Object.values(STATS).map(statType => (
          <StatBar
            key={statType}
            statType={statType}
            value={stats[statType]}
            previousValue={previousStats ? previousStats[statType] : null}
            showChange={showChanges}
            animated={true}
          />
        ))}
      </div>
      
      {!compact && (
        <div className="stats-summary">
          <div className="summary-item">
            <span className="summary-label">Strongest Area:</span>
            <span className="summary-value">{getStrongestStat(stats)}</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Needs Attention:</span>
            <span className="summary-value">{getWeakestStat(stats)}</span>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function to get strongest stat
const getStrongestStat = (stats) => {
  const maxStat = Math.max(...Object.values(stats));
  const strongestStatKey = Object.keys(stats).find(key => stats[key] === maxStat);
  return strongestStatKey ? formatStatName(strongestStatKey) : 'None';
};

// Helper function to get weakest stat
const getWeakestStat = (stats) => {
  const minStat = Math.min(...Object.values(stats));
  const weakestStatKey = Object.keys(stats).find(key => stats[key] === minStat);
  return weakestStatKey ? formatStatName(weakestStatKey) : 'None';
};

// Helper function to format stat name
const formatStatName = (statType) => {
  const names = {
    health: 'Health',
    happiness: 'Happiness',
    money: 'Money',
    relationships: 'Relationships'
  };
  return names[statType] || statType;
};

export default StatsDashboard;
