/* ChoiceCard component styles */

.choice-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.choice-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(102, 126, 234, 0.5);
}

.choice-card.hovered {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.05));
}

.choice-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.choice-card.disabled:hover {
  box-shadow: none;
  border-color: rgba(255, 255, 255, 0.2);
}

.choice-content {
  position: relative;
  z-index: 2;
  flex: 1;
}

.choice-text {
  font-size: 1.1em;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 16px;
}

.choice-effects {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.effects-header {
  margin-bottom: 12px;
}

.effects-label {
  font-size: 0.85em;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.effects-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.effect-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85em;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
}

.effect-icon {
  font-size: 0.9em;
}

.effect-name {
  flex: 1;
  font-size: 0.8em;
}

.effect-value {
  font-weight: 700;
  font-size: 0.9em;
}

.choice-hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 3;
  border-radius: 14px;
}

.choice-card:hover .choice-hover-overlay {
  opacity: 1;
}

.choice-card.disabled:hover .choice-hover-overlay {
  opacity: 0;
}

.hover-text {
  color: white;
  font-size: 1.1em;
  font-weight: 700;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.choice-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.3);
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
  z-index: 1;
}

.choice-card:active .choice-ripple {
  width: 300px;
  height: 300px;
}

/* Animation for card entrance */
.choice-card {
  animation: cardSlideIn 0.6s ease-out;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .choice-card {
    padding: 20px;
    min-height: 100px;
  }
  
  .choice-text {
    font-size: 1em;
    margin-bottom: 12px;
  }
  
  .effects-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }
  
  .effect-item {
    font-size: 0.8em;
    padding: 3px 6px;
  }
  
  .hover-text {
    font-size: 1em;
  }
}

@media (max-width: 480px) {
  .choice-card {
    padding: 16px;
    min-height: 80px;
  }
  
  .choice-text {
    font-size: 0.95em;
  }
  
  .effects-label {
    font-size: 0.8em;
  }
  
  .effect-item {
    font-size: 0.75em;
  }
  
  .choice-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .choice-card {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
    border: 2px solid rgba(255, 255, 255, 0.1);
  }
  
  .choice-card:hover {
    border-color: rgba(168, 184, 240, 0.5);
  }
  
  .choice-card.hovered {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.1));
  }
  
  .choice-text {
    color: #ecf0f1;
  }
  
  .effects-label {
    color: #bdc3c7;
  }
  
  .effect-item {
    background: rgba(0, 0, 0, 0.3);
  }
  
  .choice-effects {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}
