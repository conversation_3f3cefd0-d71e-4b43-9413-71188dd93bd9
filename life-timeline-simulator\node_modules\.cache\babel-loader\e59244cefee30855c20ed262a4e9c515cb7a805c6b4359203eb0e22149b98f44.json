{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LTS\\\\life-timeline-simulator\\\\src\\\\components\\\\StatBar.js\";\n// Individual stat bar component with animations\n\nimport React from 'react';\nimport { getStatColor, getStatIcon, formatStatName } from '../utils/gameUtils';\nimport '../styles/StatBar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatBar = ({\n  statType,\n  value,\n  previousValue = null,\n  showChange = false,\n  animated = true\n}) => {\n  const color = getStatColor(value);\n  const icon = getStatIcon(statType);\n  const name = formatStatName(statType);\n\n  // Calculate change if previous value is provided\n  const change = previousValue !== null ? value - previousValue : 0;\n  const hasChange = showChange && change !== 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `stat-bar ${animated ? 'animated' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-icon\",\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-name\",\n          children: name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-value-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-value\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), hasChange && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `stat-change ${change > 0 ? 'positive' : 'negative'}`,\n          children: [change > 0 ? '+' : '', change]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-bar-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-bar-fill\",\n        style: {\n          width: `${value}%`,\n          backgroundColor: color,\n          transition: animated ? 'width 0.8s ease-in-out, background-color 0.3s ease' : 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-bar-background\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-description\",\n      children: getStatDescription(statType, value)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n\n// Helper function to get stat description based on value\n_c = StatBar;\nconst getStatDescription = (statType, value) => {\n  const descriptions = {\n    health: {\n      high: \"Excellent physical condition\",\n      good: \"Good health and energy\",\n      average: \"Decent health\",\n      low: \"Health concerns\",\n      critical: \"Poor health\"\n    },\n    happiness: {\n      high: \"Living your best life\",\n      good: \"Generally content\",\n      average: \"Ups and downs\",\n      low: \"Struggling with mood\",\n      critical: \"Feeling quite down\"\n    },\n    money: {\n      high: \"Financially secure\",\n      good: \"Comfortable income\",\n      average: \"Getting by\",\n      low: \"Money is tight\",\n      critical: \"Financial struggles\"\n    },\n    relationships: {\n      high: \"Strong social connections\",\n      good: \"Good relationships\",\n      average: \"Some close friends\",\n      low: \"Few close relationships\",\n      critical: \"Feeling isolated\"\n    }\n  };\n  const statDescriptions = descriptions[statType] || {};\n  if (value >= 80) return statDescriptions.high || \"Excellent\";\n  if (value >= 60) return statDescriptions.good || \"Good\";\n  if (value >= 40) return statDescriptions.average || \"Average\";\n  if (value >= 20) return statDescriptions.low || \"Low\";\n  return statDescriptions.critical || \"Critical\";\n};\nexport default StatBar;\nvar _c;\n$RefreshReg$(_c, \"StatBar\");", "map": {"version": 3, "names": ["React", "getStatColor", "getStatIcon", "formatStatName", "jsxDEV", "_jsxDEV", "StatBar", "statType", "value", "previousValue", "showChange", "animated", "color", "icon", "name", "change", "hasChange", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "backgroundColor", "transition", "getStatDescription", "_c", "descriptions", "health", "high", "good", "average", "low", "critical", "happiness", "money", "relationships", "statDescriptions", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/components/StatBar.js"], "sourcesContent": ["// Individual stat bar component with animations\n\nimport React from 'react';\nimport { getStatColor, getStatIcon, formatStatName } from '../utils/gameUtils';\nimport '../styles/StatBar.css';\n\nconst StatBar = ({ \n  statType, \n  value, \n  previousValue = null, \n  showChange = false,\n  animated = true \n}) => {\n  const color = getStatColor(value);\n  const icon = getStatIcon(statType);\n  const name = formatStatName(statType);\n  \n  // Calculate change if previous value is provided\n  const change = previousValue !== null ? value - previousValue : 0;\n  const hasChange = showChange && change !== 0;\n  \n  return (\n    <div className={`stat-bar ${animated ? 'animated' : ''}`}>\n      <div className=\"stat-header\">\n        <div className=\"stat-info\">\n          <span className=\"stat-icon\">{icon}</span>\n          <span className=\"stat-name\">{name}</span>\n        </div>\n        <div className=\"stat-value-container\">\n          <span className=\"stat-value\">{value}</span>\n          {hasChange && (\n            <span className={`stat-change ${change > 0 ? 'positive' : 'negative'}`}>\n              {change > 0 ? '+' : ''}{change}\n            </span>\n          )}\n        </div>\n      </div>\n      \n      <div className=\"stat-bar-container\">\n        <div \n          className=\"stat-bar-fill\"\n          style={{\n            width: `${value}%`,\n            backgroundColor: color,\n            transition: animated ? 'width 0.8s ease-in-out, background-color 0.3s ease' : 'none'\n          }}\n        />\n        <div className=\"stat-bar-background\" />\n      </div>\n      \n      <div className=\"stat-description\">\n        {getStatDescription(statType, value)}\n      </div>\n    </div>\n  );\n};\n\n// Helper function to get stat description based on value\nconst getStatDescription = (statType, value) => {\n  const descriptions = {\n    health: {\n      high: \"Excellent physical condition\",\n      good: \"Good health and energy\",\n      average: \"Decent health\",\n      low: \"Health concerns\",\n      critical: \"Poor health\"\n    },\n    happiness: {\n      high: \"Living your best life\",\n      good: \"Generally content\",\n      average: \"Ups and downs\",\n      low: \"Struggling with mood\",\n      critical: \"Feeling quite down\"\n    },\n    money: {\n      high: \"Financially secure\",\n      good: \"Comfortable income\",\n      average: \"Getting by\",\n      low: \"Money is tight\",\n      critical: \"Financial struggles\"\n    },\n    relationships: {\n      high: \"Strong social connections\",\n      good: \"Good relationships\",\n      average: \"Some close friends\",\n      low: \"Few close relationships\",\n      critical: \"Feeling isolated\"\n    }\n  };\n\n  const statDescriptions = descriptions[statType] || {};\n  \n  if (value >= 80) return statDescriptions.high || \"Excellent\";\n  if (value >= 60) return statDescriptions.good || \"Good\";\n  if (value >= 40) return statDescriptions.average || \"Average\";\n  if (value >= 20) return statDescriptions.low || \"Low\";\n  return statDescriptions.critical || \"Critical\";\n};\n\nexport default StatBar;\n"], "mappings": ";AAAA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,EAAEC,WAAW,EAAEC,cAAc,QAAQ,oBAAoB;AAC9E,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,OAAO,GAAGA,CAAC;EACfC,QAAQ;EACRC,KAAK;EACLC,aAAa,GAAG,IAAI;EACpBC,UAAU,GAAG,KAAK;EAClBC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAGX,YAAY,CAACO,KAAK,CAAC;EACjC,MAAMK,IAAI,GAAGX,WAAW,CAACK,QAAQ,CAAC;EAClC,MAAMO,IAAI,GAAGX,cAAc,CAACI,QAAQ,CAAC;;EAErC;EACA,MAAMQ,MAAM,GAAGN,aAAa,KAAK,IAAI,GAAGD,KAAK,GAAGC,aAAa,GAAG,CAAC;EACjE,MAAMO,SAAS,GAAGN,UAAU,IAAIK,MAAM,KAAK,CAAC;EAE5C,oBACEV,OAAA;IAAKY,SAAS,EAAE,YAAYN,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;IAAAO,QAAA,gBACvDb,OAAA;MAAKY,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1Bb,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBb,OAAA;UAAMY,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEL;QAAI;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzCjB,OAAA;UAAMY,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEJ;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACNjB,OAAA;QAAKY,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCb,OAAA;UAAMY,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEV;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC1CN,SAAS,iBACRX,OAAA;UAAMY,SAAS,EAAE,eAAeF,MAAM,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU,EAAG;UAAAG,QAAA,GACpEH,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,MAAM;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjB,OAAA;MAAKY,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCb,OAAA;QACEY,SAAS,EAAC,eAAe;QACzBM,KAAK,EAAE;UACLC,KAAK,EAAE,GAAGhB,KAAK,GAAG;UAClBiB,eAAe,EAAEb,KAAK;UACtBc,UAAU,EAAEf,QAAQ,GAAG,oDAAoD,GAAG;QAChF;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFjB,OAAA;QAAKY,SAAS,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAENjB,OAAA;MAAKY,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BS,kBAAkB,CAACpB,QAAQ,EAAEC,KAAK;IAAC;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAM,EAAA,GAnDMtB,OAAO;AAoDb,MAAMqB,kBAAkB,GAAGA,CAACpB,QAAQ,EAAEC,KAAK,KAAK;EAC9C,MAAMqB,YAAY,GAAG;IACnBC,MAAM,EAAE;MACNC,IAAI,EAAE,8BAA8B;MACpCC,IAAI,EAAE,wBAAwB;MAC9BC,OAAO,EAAE,eAAe;MACxBC,GAAG,EAAE,iBAAiB;MACtBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACTL,IAAI,EAAE,uBAAuB;MAC7BC,IAAI,EAAE,mBAAmB;MACzBC,OAAO,EAAE,eAAe;MACxBC,GAAG,EAAE,sBAAsB;MAC3BC,QAAQ,EAAE;IACZ,CAAC;IACDE,KAAK,EAAE;MACLN,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAE,oBAAoB;MAC1BC,OAAO,EAAE,YAAY;MACrBC,GAAG,EAAE,gBAAgB;MACrBC,QAAQ,EAAE;IACZ,CAAC;IACDG,aAAa,EAAE;MACbP,IAAI,EAAE,2BAA2B;MACjCC,IAAI,EAAE,oBAAoB;MAC1BC,OAAO,EAAE,oBAAoB;MAC7BC,GAAG,EAAE,yBAAyB;MAC9BC,QAAQ,EAAE;IACZ;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAGV,YAAY,CAACtB,QAAQ,CAAC,IAAI,CAAC,CAAC;EAErD,IAAIC,KAAK,IAAI,EAAE,EAAE,OAAO+B,gBAAgB,CAACR,IAAI,IAAI,WAAW;EAC5D,IAAIvB,KAAK,IAAI,EAAE,EAAE,OAAO+B,gBAAgB,CAACP,IAAI,IAAI,MAAM;EACvD,IAAIxB,KAAK,IAAI,EAAE,EAAE,OAAO+B,gBAAgB,CAACN,OAAO,IAAI,SAAS;EAC7D,IAAIzB,KAAK,IAAI,EAAE,EAAE,OAAO+B,gBAAgB,CAACL,GAAG,IAAI,KAAK;EACrD,OAAOK,gBAAgB,CAACJ,QAAQ,IAAI,UAAU;AAChD,CAAC;AAED,eAAe7B,OAAO;AAAC,IAAAsB,EAAA;AAAAY,YAAA,CAAAZ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}