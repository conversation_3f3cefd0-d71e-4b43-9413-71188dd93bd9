{"ast": null, "code": "// Game constants and data structures for Life Timeline Simulator\n\nexport const LIFE_STAGES = {\n  CHILDHOOD: 'childhood',\n  TEEN: 'teen',\n  YOUNG_ADULT: 'young_adult',\n  ADULT: 'adult',\n  OLD_AGE: 'old_age'\n};\nexport const STATS = {\n  HEALTH: 'health',\n  HAPPINESS: 'happiness',\n  MONEY: 'money',\n  RELATIONSHIPS: 'relationships'\n};\n\n// Initial stats for new game\nexport const INITIAL_STATS = {\n  [STATS.HEALTH]: 80,\n  [STATS.HAPPINESS]: 70,\n  [STATS.MONEY]: 20,\n  [STATS.RELATIONSHIPS]: 60\n};\n\n// Life stage configurations\nexport const STAGE_CONFIG = {\n  [LIFE_STAGES.CHILDHOOD]: {\n    name: 'Childhood',\n    description: 'The early years of discovery and learning',\n    ageRange: '5-12',\n    icon: '🧒',\n    color: '#FFB6C1'\n  },\n  [LIFE_STAGES.TEEN]: {\n    name: 'Teenage Years',\n    description: 'Finding your identity and making first big decisions',\n    ageRange: '13-18',\n    icon: '🧑‍🎓',\n    color: '#87CEEB'\n  },\n  [LIFE_STAGES.YOUNG_ADULT]: {\n    name: 'Young Adult',\n    description: 'Building your career and relationships',\n    ageRange: '19-30',\n    icon: '👨‍💼',\n    color: '#98FB98'\n  },\n  [LIFE_STAGES.ADULT]: {\n    name: 'Adult',\n    description: 'Establishing your life and making major decisions',\n    ageRange: '31-55',\n    icon: '👨‍👩‍👧‍👦',\n    color: '#DDA0DD'\n  },\n  [LIFE_STAGES.OLD_AGE]: {\n    name: 'Golden Years',\n    description: 'Reflecting on life and enjoying the fruits of your labor',\n    ageRange: '56+',\n    icon: '👴',\n    color: '#F0E68C'\n  }\n};\n\n// Choices for each life stage\nexport const LIFE_CHOICES = {\n  [LIFE_STAGES.CHILDHOOD]: [{\n    id: 'childhood_1',\n    title: 'School vs Play',\n    description: 'How do you spend your free time?',\n    options: [{\n      text: 'Focus on studies and homework',\n      effects: {\n        health: -5,\n        happiness: -10,\n        money: 0,\n        relationships: -5\n      },\n      story: 'You became a dedicated student, always finishing homework first.'\n    }, {\n      text: 'Play with friends every day',\n      effects: {\n        health: 10,\n        happiness: 15,\n        money: 0,\n        relationships: 10\n      },\n      story: 'You made many friends and had countless adventures playing outside.'\n    }, {\n      text: 'Balance both study and play',\n      effects: {\n        health: 5,\n        happiness: 5,\n        money: 0,\n        relationships: 5\n      },\n      story: 'You learned the importance of balance early in life.'\n    }]\n  }, {\n    id: 'childhood_2',\n    title: 'Family Time',\n    description: 'Your family wants to spend more time together',\n    options: [{\n      text: 'Spend lots of time with family',\n      effects: {\n        health: 5,\n        happiness: 10,\n        money: 0,\n        relationships: 15\n      },\n      story: 'You built strong family bonds that would last a lifetime.'\n    }, {\n      text: 'Prefer independence and alone time',\n      effects: {\n        health: 0,\n        happiness: -5,\n        money: 0,\n        relationships: -10\n      },\n      story: 'You developed strong independence but missed some family moments.'\n    }, {\n      text: 'Join family activities occasionally',\n      effects: {\n        health: 2,\n        happiness: 5,\n        money: 0,\n        relationships: 8\n      },\n      story: 'You maintained good family relationships while keeping some independence.'\n    }]\n  }],\n  [LIFE_STAGES.TEEN]: [{\n    id: 'teen_1',\n    title: 'Academic Path',\n    description: 'What kind of student are you?',\n    options: [{\n      text: 'Straight-A student, very focused',\n      effects: {\n        health: -10,\n        happiness: -5,\n        money: 5,\n        relationships: -5\n      },\n      story: 'You excelled academically but missed some social experiences.'\n    }, {\n      text: 'Social butterfly, average grades',\n      effects: {\n        health: 5,\n        happiness: 15,\n        money: -5,\n        relationships: 20\n      },\n      story: 'You were popular and well-liked, though grades suffered slightly.'\n    }, {\n      text: 'Well-rounded student',\n      effects: {\n        health: 0,\n        happiness: 8,\n        money: 2,\n        relationships: 10\n      },\n      story: 'You maintained good grades while enjoying a healthy social life.'\n    }]\n  }, {\n    id: 'teen_2',\n    title: 'First Job',\n    description: 'Do you get a part-time job?',\n    options: [{\n      text: 'Work part-time after school',\n      effects: {\n        health: -5,\n        happiness: -5,\n        money: 20,\n        relationships: -5\n      },\n      story: 'You learned the value of hard work and earned your first paycheck.'\n    }, {\n      text: 'Focus on school and activities',\n      effects: {\n        health: 5,\n        happiness: 10,\n        money: -5,\n        relationships: 10\n      },\n      story: 'You enjoyed your teenage years without work pressure.'\n    }, {\n      text: 'Volunteer in the community',\n      effects: {\n        health: 0,\n        happiness: 15,\n        money: 0,\n        relationships: 15\n      },\n      story: 'You gave back to the community and made meaningful connections.'\n    }]\n  }],\n  [LIFE_STAGES.YOUNG_ADULT]: [{\n    id: 'young_adult_1',\n    title: 'Career Choice',\n    description: 'What career path do you choose?',\n    options: [{\n      text: 'High-paying corporate job',\n      effects: {\n        health: -10,\n        happiness: -5,\n        money: 30,\n        relationships: -10\n      },\n      story: 'You climbed the corporate ladder quickly but worked long hours.'\n    }, {\n      text: 'Follow your passion (lower pay)',\n      effects: {\n        health: 5,\n        happiness: 25,\n        money: -10,\n        relationships: 5\n      },\n      story: 'You pursued your dreams and found fulfillment in your work.'\n    }, {\n      text: 'Stable government job',\n      effects: {\n        health: 5,\n        happiness: 10,\n        money: 15,\n        relationships: 5\n      },\n      story: 'You chose security and work-life balance over high earnings.'\n    }]\n  }, {\n    id: 'young_adult_2',\n    title: 'Relationships',\n    description: 'How do you approach romantic relationships?',\n    options: [{\n      text: 'Focus on career, date casually',\n      effects: {\n        health: 0,\n        happiness: 5,\n        money: 10,\n        relationships: -5\n      },\n      story: 'You prioritized your career and kept relationships light.'\n    }, {\n      text: 'Seek serious long-term relationship',\n      effects: {\n        health: 10,\n        happiness: 15,\n        money: -5,\n        relationships: 25\n      },\n      story: 'You found love and built a strong partnership.'\n    }, {\n      text: 'Stay single and independent',\n      effects: {\n        health: 5,\n        happiness: 0,\n        money: 5,\n        relationships: -10\n      },\n      story: 'You enjoyed your independence and focused on personal growth.'\n    }]\n  }],\n  [LIFE_STAGES.ADULT]: [{\n    id: 'adult_1',\n    title: 'Family Planning',\n    description: 'Do you want to start a family?',\n    options: [{\n      text: 'Have children and focus on family',\n      effects: {\n        health: -5,\n        happiness: 20,\n        money: -20,\n        relationships: 20\n      },\n      story: 'You became a parent and found joy in raising children.'\n    }, {\n      text: 'Focus on career advancement',\n      effects: {\n        health: -10,\n        happiness: 5,\n        money: 25,\n        relationships: -5\n      },\n      story: 'You climbed to executive levels and achieved financial success.'\n    }, {\n      text: 'Travel and enjoy life experiences',\n      effects: {\n        health: 10,\n        happiness: 15,\n        money: -10,\n        relationships: 10\n      },\n      story: 'You explored the world and collected amazing memories.'\n    }]\n  }, {\n    id: 'adult_2',\n    title: 'Financial Decisions',\n    description: 'How do you manage your money?',\n    options: [{\n      text: 'Save and invest aggressively',\n      effects: {\n        health: 0,\n        happiness: -5,\n        money: 30,\n        relationships: -5\n      },\n      story: 'You built substantial wealth through careful planning and investing.'\n    }, {\n      text: 'Spend on experiences and enjoyment',\n      effects: {\n        health: 5,\n        happiness: 20,\n        money: -15,\n        relationships: 10\n      },\n      story: 'You lived life to the fullest and created wonderful memories.'\n    }, {\n      text: 'Balance saving and spending',\n      effects: {\n        health: 2,\n        happiness: 10,\n        money: 10,\n        relationships: 5\n      },\n      story: 'You found a healthy balance between enjoying life and planning for the future.'\n    }]\n  }],\n  [LIFE_STAGES.OLD_AGE]: [{\n    id: 'old_age_1',\n    title: 'Retirement Lifestyle',\n    description: 'How do you spend your golden years?',\n    options: [{\n      text: 'Stay active and pursue hobbies',\n      effects: {\n        health: 15,\n        happiness: 20,\n        money: -5,\n        relationships: 10\n      },\n      story: 'You stayed vibrant and discovered new passions in retirement.'\n    }, {\n      text: 'Relax and take it easy',\n      effects: {\n        health: -5,\n        happiness: 10,\n        money: 5,\n        relationships: 0\n      },\n      story: 'You enjoyed a peaceful and restful retirement.'\n    }, {\n      text: 'Volunteer and help others',\n      effects: {\n        health: 5,\n        happiness: 25,\n        money: 0,\n        relationships: 20\n      },\n      story: 'You found purpose in giving back to your community.'\n    }]\n  }]\n};\n\n// Stat limits\nexport const STAT_LIMITS = {\n  MIN: 0,\n  MAX: 100\n};", "map": {"version": 3, "names": ["LIFE_STAGES", "CHILDHOOD", "TEEN", "YOUNG_ADULT", "ADULT", "OLD_AGE", "STATS", "HEALTH", "HAPPINESS", "MONEY", "RELATIONSHIPS", "INITIAL_STATS", "STAGE_CONFIG", "name", "description", "<PERSON><PERSON><PERSON><PERSON>", "icon", "color", "LIFE_CHOICES", "id", "title", "options", "text", "effects", "health", "happiness", "money", "relationships", "story", "STAT_LIMITS", "MIN", "MAX"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/data/gameData.js"], "sourcesContent": ["// Game constants and data structures for Life Timeline Simulator\n\nexport const LIFE_STAGES = {\n  CHILDHOOD: 'childhood',\n  TEEN: 'teen',\n  YOUNG_ADULT: 'young_adult',\n  ADULT: 'adult',\n  OLD_AGE: 'old_age'\n};\n\nexport const STATS = {\n  HEALTH: 'health',\n  HAPPINESS: 'happiness',\n  MONEY: 'money',\n  RELATIONSHIPS: 'relationships'\n};\n\n// Initial stats for new game\nexport const INITIAL_STATS = {\n  [STATS.HEALTH]: 80,\n  [STATS.HAPPINESS]: 70,\n  [STATS.MONEY]: 20,\n  [STATS.RELATIONSHIPS]: 60\n};\n\n// Life stage configurations\nexport const STAGE_CONFIG = {\n  [LIFE_STAGES.CHILDHOOD]: {\n    name: 'Childhood',\n    description: 'The early years of discovery and learning',\n    ageRange: '5-12',\n    icon: '🧒',\n    color: '#FFB6C1'\n  },\n  [LIFE_STAGES.TEEN]: {\n    name: 'Teenage Years',\n    description: 'Finding your identity and making first big decisions',\n    ageRange: '13-18',\n    icon: '🧑‍🎓',\n    color: '#87CEEB'\n  },\n  [LIFE_STAGES.YOUNG_ADULT]: {\n    name: 'Young Adult',\n    description: 'Building your career and relationships',\n    ageRange: '19-30',\n    icon: '👨‍💼',\n    color: '#98FB98'\n  },\n  [LIFE_STAGES.ADULT]: {\n    name: 'Adult',\n    description: 'Establishing your life and making major decisions',\n    ageRange: '31-55',\n    icon: '👨‍👩‍👧‍👦',\n    color: '#DDA0DD'\n  },\n  [LIFE_STAGES.OLD_AGE]: {\n    name: 'Golden Years',\n    description: 'Reflecting on life and enjoying the fruits of your labor',\n    ageRange: '56+',\n    icon: '👴',\n    color: '#F0E68C'\n  }\n};\n\n// Choices for each life stage\nexport const LIFE_CHOICES = {\n  [LIFE_STAGES.CHILDHOOD]: [\n    {\n      id: 'childhood_1',\n      title: 'School vs Play',\n      description: 'How do you spend your free time?',\n      options: [\n        {\n          text: 'Focus on studies and homework',\n          effects: { health: -5, happiness: -10, money: 0, relationships: -5 },\n          story: 'You became a dedicated student, always finishing homework first.'\n        },\n        {\n          text: 'Play with friends every day',\n          effects: { health: 10, happiness: 15, money: 0, relationships: 10 },\n          story: 'You made many friends and had countless adventures playing outside.'\n        },\n        {\n          text: 'Balance both study and play',\n          effects: { health: 5, happiness: 5, money: 0, relationships: 5 },\n          story: 'You learned the importance of balance early in life.'\n        }\n      ]\n    },\n    {\n      id: 'childhood_2',\n      title: 'Family Time',\n      description: 'Your family wants to spend more time together',\n      options: [\n        {\n          text: 'Spend lots of time with family',\n          effects: { health: 5, happiness: 10, money: 0, relationships: 15 },\n          story: 'You built strong family bonds that would last a lifetime.'\n        },\n        {\n          text: 'Prefer independence and alone time',\n          effects: { health: 0, happiness: -5, money: 0, relationships: -10 },\n          story: 'You developed strong independence but missed some family moments.'\n        },\n        {\n          text: 'Join family activities occasionally',\n          effects: { health: 2, happiness: 5, money: 0, relationships: 8 },\n          story: 'You maintained good family relationships while keeping some independence.'\n        }\n      ]\n    }\n  ],\n\n  [LIFE_STAGES.TEEN]: [\n    {\n      id: 'teen_1',\n      title: 'Academic Path',\n      description: 'What kind of student are you?',\n      options: [\n        {\n          text: 'Straight-A student, very focused',\n          effects: { health: -10, happiness: -5, money: 5, relationships: -5 },\n          story: 'You excelled academically but missed some social experiences.'\n        },\n        {\n          text: 'Social butterfly, average grades',\n          effects: { health: 5, happiness: 15, money: -5, relationships: 20 },\n          story: 'You were popular and well-liked, though grades suffered slightly.'\n        },\n        {\n          text: 'Well-rounded student',\n          effects: { health: 0, happiness: 8, money: 2, relationships: 10 },\n          story: 'You maintained good grades while enjoying a healthy social life.'\n        }\n      ]\n    },\n    {\n      id: 'teen_2',\n      title: 'First Job',\n      description: 'Do you get a part-time job?',\n      options: [\n        {\n          text: 'Work part-time after school',\n          effects: { health: -5, happiness: -5, money: 20, relationships: -5 },\n          story: 'You learned the value of hard work and earned your first paycheck.'\n        },\n        {\n          text: 'Focus on school and activities',\n          effects: { health: 5, happiness: 10, money: -5, relationships: 10 },\n          story: 'You enjoyed your teenage years without work pressure.'\n        },\n        {\n          text: 'Volunteer in the community',\n          effects: { health: 0, happiness: 15, money: 0, relationships: 15 },\n          story: 'You gave back to the community and made meaningful connections.'\n        }\n      ]\n    }\n  ],\n\n  [LIFE_STAGES.YOUNG_ADULT]: [\n    {\n      id: 'young_adult_1',\n      title: 'Career Choice',\n      description: 'What career path do you choose?',\n      options: [\n        {\n          text: 'High-paying corporate job',\n          effects: { health: -10, happiness: -5, money: 30, relationships: -10 },\n          story: 'You climbed the corporate ladder quickly but worked long hours.'\n        },\n        {\n          text: 'Follow your passion (lower pay)',\n          effects: { health: 5, happiness: 25, money: -10, relationships: 5 },\n          story: 'You pursued your dreams and found fulfillment in your work.'\n        },\n        {\n          text: 'Stable government job',\n          effects: { health: 5, happiness: 10, money: 15, relationships: 5 },\n          story: 'You chose security and work-life balance over high earnings.'\n        }\n      ]\n    },\n    {\n      id: 'young_adult_2',\n      title: 'Relationships',\n      description: 'How do you approach romantic relationships?',\n      options: [\n        {\n          text: 'Focus on career, date casually',\n          effects: { health: 0, happiness: 5, money: 10, relationships: -5 },\n          story: 'You prioritized your career and kept relationships light.'\n        },\n        {\n          text: 'Seek serious long-term relationship',\n          effects: { health: 10, happiness: 15, money: -5, relationships: 25 },\n          story: 'You found love and built a strong partnership.'\n        },\n        {\n          text: 'Stay single and independent',\n          effects: { health: 5, happiness: 0, money: 5, relationships: -10 },\n          story: 'You enjoyed your independence and focused on personal growth.'\n        }\n      ]\n    }\n  ],\n\n  [LIFE_STAGES.ADULT]: [\n    {\n      id: 'adult_1',\n      title: 'Family Planning',\n      description: 'Do you want to start a family?',\n      options: [\n        {\n          text: 'Have children and focus on family',\n          effects: { health: -5, happiness: 20, money: -20, relationships: 20 },\n          story: 'You became a parent and found joy in raising children.'\n        },\n        {\n          text: 'Focus on career advancement',\n          effects: { health: -10, happiness: 5, money: 25, relationships: -5 },\n          story: 'You climbed to executive levels and achieved financial success.'\n        },\n        {\n          text: 'Travel and enjoy life experiences',\n          effects: { health: 10, happiness: 15, money: -10, relationships: 10 },\n          story: 'You explored the world and collected amazing memories.'\n        }\n      ]\n    },\n    {\n      id: 'adult_2',\n      title: 'Financial Decisions',\n      description: 'How do you manage your money?',\n      options: [\n        {\n          text: 'Save and invest aggressively',\n          effects: { health: 0, happiness: -5, money: 30, relationships: -5 },\n          story: 'You built substantial wealth through careful planning and investing.'\n        },\n        {\n          text: 'Spend on experiences and enjoyment',\n          effects: { health: 5, happiness: 20, money: -15, relationships: 10 },\n          story: 'You lived life to the fullest and created wonderful memories.'\n        },\n        {\n          text: 'Balance saving and spending',\n          effects: { health: 2, happiness: 10, money: 10, relationships: 5 },\n          story: 'You found a healthy balance between enjoying life and planning for the future.'\n        }\n      ]\n    }\n  ],\n\n  [LIFE_STAGES.OLD_AGE]: [\n    {\n      id: 'old_age_1',\n      title: 'Retirement Lifestyle',\n      description: 'How do you spend your golden years?',\n      options: [\n        {\n          text: 'Stay active and pursue hobbies',\n          effects: { health: 15, happiness: 20, money: -5, relationships: 10 },\n          story: 'You stayed vibrant and discovered new passions in retirement.'\n        },\n        {\n          text: 'Relax and take it easy',\n          effects: { health: -5, happiness: 10, money: 5, relationships: 0 },\n          story: 'You enjoyed a peaceful and restful retirement.'\n        },\n        {\n          text: 'Volunteer and help others',\n          effects: { health: 5, happiness: 25, money: 0, relationships: 20 },\n          story: 'You found purpose in giving back to your community.'\n        }\n      ]\n    }\n  ]\n};\n\n// Stat limits\nexport const STAT_LIMITS = {\n  MIN: 0,\n  MAX: 100\n};\n"], "mappings": "AAAA;;AAEA,OAAO,MAAMA,WAAW,GAAG;EACzBC,SAAS,EAAE,WAAW;EACtBC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,aAAa;EAC1BC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE;AACX,CAAC;AAED,OAAO,MAAMC,KAAK,GAAG;EACnBC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE,OAAO;EACdC,aAAa,EAAE;AACjB,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3B,CAACL,KAAK,CAACC,MAAM,GAAG,EAAE;EAClB,CAACD,KAAK,CAACE,SAAS,GAAG,EAAE;EACrB,CAACF,KAAK,CAACG,KAAK,GAAG,EAAE;EACjB,CAACH,KAAK,CAACI,aAAa,GAAG;AACzB,CAAC;;AAED;AACA,OAAO,MAAME,YAAY,GAAG;EAC1B,CAACZ,WAAW,CAACC,SAAS,GAAG;IACvBY,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE,2CAA2C;IACxDC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC;EACD,CAACjB,WAAW,CAACE,IAAI,GAAG;IAClBW,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE,sDAAsD;IACnEC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE;EACT,CAAC;EACD,CAACjB,WAAW,CAACG,WAAW,GAAG;IACzBU,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,wCAAwC;IACrDC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE;EACT,CAAC;EACD,CAACjB,WAAW,CAACI,KAAK,GAAG;IACnBS,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,mDAAmD;IAChEC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,CAACjB,WAAW,CAACK,OAAO,GAAG;IACrBQ,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE,0DAA0D;IACvEC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1B,CAAClB,WAAW,CAACC,SAAS,GAAG,CACvB;IACEkB,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,gBAAgB;IACvBN,WAAW,EAAE,kCAAkC;IAC/CO,OAAO,EAAE,CACP;MACEC,IAAI,EAAE,+BAA+B;MACrCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC,CAAC;QAAEC,SAAS,EAAE,CAAC,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE,CAAC;MAAE,CAAC;MACpEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,6BAA6B;MACnCC,OAAO,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAG,CAAC;MACnEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,6BAA6B;MACnCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAE,CAAC;MAChEC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACET,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,aAAa;IACpBN,WAAW,EAAE,+CAA+C;IAC5DO,OAAO,EAAE,CACP;MACEC,IAAI,EAAE,gCAAgC;MACtCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAG,CAAC;MAClEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,oCAAoC;MAC1CC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE,CAAC;MAAG,CAAC;MACnEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,qCAAqC;MAC3CC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAE,CAAC;MAChEC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CACF;EAED,CAAC5B,WAAW,CAACE,IAAI,GAAG,CAClB;IACEiB,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,eAAe;IACtBN,WAAW,EAAE,+BAA+B;IAC5CO,OAAO,EAAE,CACP;MACEC,IAAI,EAAE,kCAAkC;MACxCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE,CAAC;MAAE,CAAC;MACpEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,kCAAkC;MACxCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC,CAAC;QAAEC,aAAa,EAAE;MAAG,CAAC;MACnEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,sBAAsB;MAC5BC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAG,CAAC;MACjEC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,WAAW;IAClBN,WAAW,EAAE,6BAA6B;IAC1CO,OAAO,EAAE,CACP;MACEC,IAAI,EAAE,6BAA6B;MACnCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC,CAAC;QAAEC,SAAS,EAAE,CAAC,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,aAAa,EAAE,CAAC;MAAE,CAAC;MACpEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,gCAAgC;MACtCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC,CAAC;QAAEC,aAAa,EAAE;MAAG,CAAC;MACnEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,4BAA4B;MAClCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAG,CAAC;MAClEC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CACF;EAED,CAAC5B,WAAW,CAACG,WAAW,GAAG,CACzB;IACEgB,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,eAAe;IACtBN,WAAW,EAAE,iCAAiC;IAC9CO,OAAO,EAAE,CACP;MACEC,IAAI,EAAE,2BAA2B;MACjCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,aAAa,EAAE,CAAC;MAAG,CAAC;MACtEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,iCAAiC;MACvCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC,EAAE;QAAEC,aAAa,EAAE;MAAE,CAAC;MACnEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,uBAAuB;MAC7BC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,aAAa,EAAE;MAAE,CAAC;MAClEC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACET,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,eAAe;IACtBN,WAAW,EAAE,6CAA6C;IAC1DO,OAAO,EAAE,CACP;MACEC,IAAI,EAAE,gCAAgC;MACtCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,aAAa,EAAE,CAAC;MAAE,CAAC;MAClEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,qCAAqC;MAC3CC,OAAO,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC,CAAC;QAAEC,aAAa,EAAE;MAAG,CAAC;MACpEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,6BAA6B;MACnCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE,CAAC;MAAG,CAAC;MAClEC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CACF;EAED,CAAC5B,WAAW,CAACI,KAAK,GAAG,CACnB;IACEe,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,iBAAiB;IACxBN,WAAW,EAAE,gCAAgC;IAC7CO,OAAO,EAAE,CACP;MACEC,IAAI,EAAE,mCAAmC;MACzCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC,EAAE;QAAEC,aAAa,EAAE;MAAG,CAAC;MACrEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,6BAA6B;MACnCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,aAAa,EAAE,CAAC;MAAE,CAAC;MACpEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,mCAAmC;MACzCC,OAAO,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC,EAAE;QAAEC,aAAa,EAAE;MAAG,CAAC;MACrEC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACET,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,qBAAqB;IAC5BN,WAAW,EAAE,+BAA+B;IAC5CO,OAAO,EAAE,CACP;MACEC,IAAI,EAAE,8BAA8B;MACpCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,aAAa,EAAE,CAAC;MAAE,CAAC;MACnEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,oCAAoC;MAC1CC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC,EAAE;QAAEC,aAAa,EAAE;MAAG,CAAC;MACpEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,6BAA6B;MACnCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,aAAa,EAAE;MAAE,CAAC;MAClEC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CACF;EAED,CAAC5B,WAAW,CAACK,OAAO,GAAG,CACrB;IACEc,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,sBAAsB;IAC7BN,WAAW,EAAE,qCAAqC;IAClDO,OAAO,EAAE,CACP;MACEC,IAAI,EAAE,gCAAgC;MACtCC,OAAO,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC,CAAC;QAAEC,aAAa,EAAE;MAAG,CAAC;MACpEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,wBAAwB;MAC9BC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAE,CAAC;MAClEC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,IAAI,EAAE,2BAA2B;MACjCC,OAAO,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAG,CAAC;MAClEC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AAEL,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}