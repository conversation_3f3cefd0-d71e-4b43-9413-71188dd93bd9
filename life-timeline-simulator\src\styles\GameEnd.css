/* GameEnd component styles */

.game-end {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

.end-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.celebration-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  font-size: 1.5em;
  opacity: 0.7;
  animation: particleFloat 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.7;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.end-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  animation: endContentSlideIn 1s ease-out;
}

@keyframes endContentSlideIn {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.end-header {
  text-align: center;
  margin-bottom: 48px;
}

.end-icon {
  font-size: 4em;
  margin-bottom: 24px;
  animation: endIconBounce 1.5s ease-out;
}

@keyframes endIconBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-30px);
  }
  60% {
    transform: translateY(-15px);
  }
}

.end-title {
  font-size: 3em;
  font-weight: 800;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  line-height: 1.1;
}

.end-subtitle {
  font-size: 1.3em;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.4;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  margin: 0 auto;
}

.life-evaluation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 48px;
  margin-bottom: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.evaluation-score {
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border: 3px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: scoreCirclePulse 2s ease-in-out infinite;
}

@keyframes scoreCirclePulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
  }
}

.score-number {
  font-size: 3em;
  font-weight: 800;
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.score-label {
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.evaluation-result {
  flex: 1;
  text-align: left;
}

.evaluation-title {
  font-size: 2.2em;
  font-weight: 700;
  color: white;
  margin-bottom: 16px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.evaluation-description {
  font-size: 1.2em;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}

.final-stats {
  margin-bottom: 48px;
}

.life-story-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 48px;
}

.story-title {
  font-size: 1.8em;
  font-weight: 700;
  color: white;
  text-align: center;
  margin-bottom: 32px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.story-timeline {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.story-chapter {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform 0.3s ease;
}

.story-chapter:hover {
  transform: translateX(10px);
}

.chapter-stage {
  font-size: 1.1em;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chapter-choice {
  font-size: 1.1em;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.chapter-story {
  font-size: 1em;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
  line-height: 1.4;
}

.end-actions {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 48px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.action-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.action-button.secondary {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.button-icon {
  font-size: 1.2em;
}

.button-text {
  font-weight: 600;
}

.achievements {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.achievements h4 {
  font-size: 1.6em;
  font-weight: 700;
  color: white;
  margin-bottom: 24px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.achievement-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: achievementSlideIn 0.6s ease-out;
}

@keyframes achievementSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.achievement-icon {
  font-size: 1.5em;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.achievement-text {
  font-size: 0.95em;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  text-align: left;
}

/* Responsive design */
@media (max-width: 768px) {
  .game-end {
    padding: 16px;
  }
  
  .end-header {
    margin-bottom: 32px;
  }
  
  .end-icon {
    font-size: 3em;
  }
  
  .end-title {
    font-size: 2.2em;
  }
  
  .end-subtitle {
    font-size: 1.1em;
  }
  
  .life-evaluation {
    flex-direction: column;
    gap: 24px;
    padding: 24px;
  }
  
  .evaluation-result {
    text-align: center;
  }
  
  .score-circle {
    width: 120px;
    height: 120px;
  }
  
  .score-number {
    font-size: 2.5em;
  }
  
  .evaluation-title {
    font-size: 1.8em;
  }
  
  .evaluation-description {
    font-size: 1.1em;
  }
  
  .life-story-section {
    padding: 24px;
  }
  
  .story-title {
    font-size: 1.5em;
  }
  
  .end-actions {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
  
  .action-button {
    padding: 14px 28px;
    font-size: 1em;
  }
  
  .achievements {
    padding: 24px;
  }
  
  .achievement-list {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .end-title {
    font-size: 1.8em;
  }
  
  .end-subtitle {
    font-size: 1em;
  }
  
  .life-evaluation {
    padding: 20px;
  }
  
  .score-circle {
    width: 100px;
    height: 100px;
  }
  
  .score-number {
    font-size: 2em;
  }
  
  .evaluation-title {
    font-size: 1.5em;
  }
  
  .evaluation-description {
    font-size: 1em;
  }
  
  .life-story-section {
    padding: 20px;
  }
  
  .story-chapter {
    padding: 16px;
  }
  
  .action-button {
    padding: 12px 24px;
    font-size: 0.95em;
  }
  
  .achievements {
    padding: 20px;
  }
  
  .achievements h4 {
    font-size: 1.4em;
  }
}
