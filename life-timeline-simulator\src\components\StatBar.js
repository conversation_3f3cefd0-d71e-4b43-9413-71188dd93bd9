// Individual stat bar component with animations

import React from 'react';
import { getStatColor, getStatIcon, formatStatName } from '../utils/gameUtils';
import '../styles/StatBar.css';

const StatBar = ({ 
  statType, 
  value, 
  previousValue = null, 
  showChange = false,
  animated = true 
}) => {
  const color = getStatColor(value);
  const icon = getStatIcon(statType);
  const name = formatStatName(statType);
  
  // Calculate change if previous value is provided
  const change = previousValue !== null ? value - previousValue : 0;
  const hasChange = showChange && change !== 0;
  
  return (
    <div className={`stat-bar ${animated ? 'animated' : ''}`}>
      <div className="stat-header">
        <div className="stat-info">
          <span className="stat-icon">{icon}</span>
          <span className="stat-name">{name}</span>
        </div>
        <div className="stat-value-container">
          <span className="stat-value">{value}</span>
          {hasChange && (
            <span className={`stat-change ${change > 0 ? 'positive' : 'negative'}`}>
              {change > 0 ? '+' : ''}{change}
            </span>
          )}
        </div>
      </div>
      
      <div className="stat-bar-container">
        <div 
          className="stat-bar-fill"
          style={{
            width: `${value}%`,
            backgroundColor: color,
            transition: animated ? 'width 0.8s ease-in-out, background-color 0.3s ease' : 'none'
          }}
        />
        <div className="stat-bar-background" />
      </div>
      
      <div className="stat-description">
        {getStatDescription(statType, value)}
      </div>
    </div>
  );
};

// Helper function to get stat description based on value
const getStatDescription = (statType, value) => {
  const descriptions = {
    health: {
      high: "Excellent physical condition",
      good: "Good health and energy",
      average: "Decent health",
      low: "Health concerns",
      critical: "Poor health"
    },
    happiness: {
      high: "Living your best life",
      good: "Generally content",
      average: "Ups and downs",
      low: "Struggling with mood",
      critical: "Feeling quite down"
    },
    money: {
      high: "Financially secure",
      good: "Comfortable income",
      average: "Getting by",
      low: "Money is tight",
      critical: "Financial struggles"
    },
    relationships: {
      high: "Strong social connections",
      good: "Good relationships",
      average: "Some close friends",
      low: "Few close relationships",
      critical: "Feeling isolated"
    }
  };

  const statDescriptions = descriptions[statType] || {};
  
  if (value >= 80) return statDescriptions.high || "Excellent";
  if (value >= 60) return statDescriptions.good || "Good";
  if (value >= 40) return statDescriptions.average || "Average";
  if (value >= 20) return statDescriptions.low || "Low";
  return statDescriptions.critical || "Critical";
};

export default StatBar;
