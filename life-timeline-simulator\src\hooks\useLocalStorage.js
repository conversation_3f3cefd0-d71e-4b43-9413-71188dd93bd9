// Custom hook for localStorage management

import { useState, useEffect } from 'react';

/**
 * Custom hook for managing localStorage with React state
 * @param {string} key - localStorage key
 * @param {*} initialValue - Initial value if no stored value exists
 * @returns {Array} [storedValue, setValue] - Similar to useState
 */
export const useLocalStorage = (key, initialValue) => {
  // State to store our value
  const [storedValue, setStoredValue] = useState(() => {
    try {
      // Get from local storage by key
      const item = window.localStorage.getItem(key);
      // Parse stored json or if none return initialValue
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // If error also return initialValue
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that ...
  // ... persists the new value to localStorage.
  const setValue = (value) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to local storage
      if (valueToStore === undefined) {
        window.localStorage.removeItem(key);
      } else {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      // A more advanced implementation would handle the error case
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
};

/**
 * Hook for managing game preferences in localStorage
 * @returns {Object} Preferences object with getters and setters
 */
export const useGamePreferences = () => {
  const [preferences, setPreferences] = useLocalStorage('lts_preferences', {
    soundEnabled: true,
    animationsEnabled: true,
    theme: 'light',
    autoSave: true
  });

  const updatePreference = (key, value) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return {
    preferences,
    updatePreference,
    setSoundEnabled: (enabled) => updatePreference('soundEnabled', enabled),
    setAnimationsEnabled: (enabled) => updatePreference('animationsEnabled', enabled),
    setTheme: (theme) => updatePreference('theme', theme),
    setAutoSave: (enabled) => updatePreference('autoSave', enabled)
  };
};

/**
 * Hook for managing game statistics in localStorage
 * @returns {Object} Statistics object with getters and setters
 */
export const useGameStatistics = () => {
  const [statistics, setStatistics] = useLocalStorage('lts_statistics', {
    gamesPlayed: 0,
    gamesCompleted: 0,
    bestScore: 0,
    totalPlayTime: 0,
    favoriteEnding: null,
    achievementsUnlocked: []
  });

  const incrementGamesPlayed = () => {
    setStatistics(prev => ({
      ...prev,
      gamesPlayed: prev.gamesPlayed + 1
    }));
  };

  const incrementGamesCompleted = () => {
    setStatistics(prev => ({
      ...prev,
      gamesCompleted: prev.gamesCompleted + 1
    }));
  };

  const updateBestScore = (score) => {
    setStatistics(prev => ({
      ...prev,
      bestScore: Math.max(prev.bestScore, score)
    }));
  };

  const addPlayTime = (minutes) => {
    setStatistics(prev => ({
      ...prev,
      totalPlayTime: prev.totalPlayTime + minutes
    }));
  };

  const unlockAchievement = (achievement) => {
    setStatistics(prev => ({
      ...prev,
      achievementsUnlocked: [...new Set([...prev.achievementsUnlocked, achievement])]
    }));
  };

  return {
    statistics,
    incrementGamesPlayed,
    incrementGamesCompleted,
    updateBestScore,
    addPlayTime,
    unlockAchievement
  };
};
