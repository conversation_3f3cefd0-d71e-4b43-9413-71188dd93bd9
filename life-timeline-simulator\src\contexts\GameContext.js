// Game Context for managing global state

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { 
  LIFE_STAGES, 
  INITIAL_STATS, 
  LIFE_CHOICES 
} from '../data/gameData';
import { 
  applyStatEffects, 
  getNextStage, 
  saveGameState, 
  loadGameState,
  clearGameState 
} from '../utils/gameUtils';

// Action types
const GAME_ACTIONS = {
  START_NEW_GAME: 'START_NEW_GAME',
  LOAD_GAME: 'LOAD_GAME',
  MAKE_CHOICE: 'MAKE_CHOICE',
  ADVANCE_STAGE: 'ADVANCE_STAGE',
  RESTART_GAME: 'RESTART_GAME',
  SET_LOADING: 'SET_LOADING'
};

// Initial game state
const initialState = {
  currentStage: LIFE_STAGES.CHILDHOOD,
  currentChoiceIndex: 0,
  stats: { ...INITIAL_STATS },
  choiceHistory: [],
  gameComplete: false,
  loading: false,
  gameStarted: false
};

// Game reducer
const gameReducer = (state, action) => {
  switch (action.type) {
    case GAME_ACTIONS.START_NEW_GAME:
      return {
        ...initialState,
        gameStarted: true,
        loading: false
      };

    case GAME_ACTIONS.LOAD_GAME:
      return {
        ...action.payload,
        loading: false
      };

    case GAME_ACTIONS.MAKE_CHOICE:
      const { choice, option } = action.payload;
      const newStats = applyStatEffects(state.stats, option.effects);
      
      const newChoiceHistory = [
        ...state.choiceHistory,
        {
          stage: state.currentStage,
          choiceId: choice.id,
          choiceTitle: choice.title,
          selectedOption: option.text,
          effects: option.effects,
          story: option.story,
          timestamp: new Date().toISOString()
        }
      ];

      return {
        ...state,
        stats: newStats,
        choiceHistory: newChoiceHistory,
        currentChoiceIndex: state.currentChoiceIndex + 1
      };

    case GAME_ACTIONS.ADVANCE_STAGE:
      const nextStage = getNextStage(state.currentStage);
      
      return {
        ...state,
        currentStage: nextStage || state.currentStage,
        currentChoiceIndex: 0,
        gameComplete: nextStage === null
      };

    case GAME_ACTIONS.RESTART_GAME:
      return {
        ...initialState,
        gameStarted: true
      };

    case GAME_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    default:
      return state;
  }
};

// Create context
const GameContext = createContext();

// Custom hook to use game context
export const useGame = () => {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
};

// Game provider component
export const GameProvider = ({ children }) => {
  const [state, dispatch] = useReducer(gameReducer, initialState);

  // Auto-save game state when it changes
  useEffect(() => {
    if (state.gameStarted && !state.loading) {
      saveGameState(state);
    }
  }, [state]);

  // Game actions
  const startNewGame = () => {
    clearGameState();
    dispatch({ type: GAME_ACTIONS.START_NEW_GAME });
  };

  const loadGame = () => {
    dispatch({ type: GAME_ACTIONS.SET_LOADING, payload: true });
    
    const savedState = loadGameState();
    if (savedState) {
      dispatch({ type: GAME_ACTIONS.LOAD_GAME, payload: savedState });
    } else {
      dispatch({ type: GAME_ACTIONS.START_NEW_GAME });
    }
  };

  const makeChoice = (choice, option) => {
    dispatch({ 
      type: GAME_ACTIONS.MAKE_CHOICE, 
      payload: { choice, option } 
    });
  };

  const advanceStage = () => {
    dispatch({ type: GAME_ACTIONS.ADVANCE_STAGE });
  };

  const restartGame = () => {
    clearGameState();
    dispatch({ type: GAME_ACTIONS.RESTART_GAME });
  };

  // Get current choices for the current stage
  const getCurrentChoices = () => {
    const stageChoices = LIFE_CHOICES[state.currentStage] || [];
    return stageChoices;
  };

  // Get current choice (if any)
  const getCurrentChoice = () => {
    const choices = getCurrentChoices();
    return choices[state.currentChoiceIndex] || null;
  };

  // Check if current stage has more choices
  const hasMoreChoicesInStage = () => {
    const choices = getCurrentChoices();
    return state.currentChoiceIndex < choices.length;
  };

  // Check if there's a saved game
  const hasSavedGame = () => {
    return loadGameState() !== null;
  };

  // Get life story from choice history
  const getLifeStory = () => {
    return state.choiceHistory.map(choice => ({
      stage: choice.stage,
      story: choice.story,
      choice: choice.selectedOption
    }));
  };

  // Context value
  const value = {
    // State
    ...state,
    
    // Actions
    startNewGame,
    loadGame,
    makeChoice,
    advanceStage,
    restartGame,
    
    // Getters
    getCurrentChoices,
    getCurrentChoice,
    hasMoreChoicesInStage,
    hasSavedGame,
    getLifeStory,
    
    // Computed values
    isStageComplete: !hasMoreChoicesInStage(),
    canAdvanceStage: !hasMoreChoicesInStage() && !state.gameComplete
  };

  return (
    <GameContext.Provider value={value}>
      {children}
    </GameContext.Provider>
  );
};
