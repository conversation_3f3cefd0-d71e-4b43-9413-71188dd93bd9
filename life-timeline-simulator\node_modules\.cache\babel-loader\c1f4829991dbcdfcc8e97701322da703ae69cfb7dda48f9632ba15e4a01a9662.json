{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LTS\\\\life-timeline-simulator\\\\src\\\\components\\\\GameFlow.js\",\n  _s = $RefreshSig$();\n// Main game flow component\n\nimport React, { useState, useEffect } from 'react';\nimport { useGame } from '../contexts/GameContext';\nimport LifeStageDisplay from './LifeStageDisplay';\nimport TimelineVisualization from './TimelineVisualization';\nimport StatsDashboard from './StatsDashboard';\nimport ChoicePresentation from './ChoicePresentation';\nimport { LIFE_STAGES } from '../data/gameData';\nimport '../styles/GameFlow.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameFlow = () => {\n  _s();\n  const {\n    currentStage,\n    stats,\n    choiceHistory,\n    gameComplete,\n    getCurrentChoice,\n    hasMoreChoicesInStage,\n    canAdvanceStage,\n    makeChoice,\n    advanceStage,\n    isStageComplete\n  } = useGame();\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [previousStats, setPreviousStats] = useState(null);\n  const [showStageComplete, setShowStageComplete] = useState(false);\n  const currentChoice = getCurrentChoice();\n  const completedStages = getCompletedStages();\n\n  // Handle choice selection\n  const handleChoiceSelect = (choice, option) => {\n    setPreviousStats({\n      ...stats\n    });\n    makeChoice(choice, option);\n\n    // Check if stage is complete after this choice\n    setTimeout(() => {\n      if (!hasMoreChoicesInStage()) {\n        setShowStageComplete(true);\n      }\n    }, 1000);\n  };\n\n  // Handle stage advancement\n  const handleAdvanceStage = () => {\n    setIsTransitioning(true);\n    setShowStageComplete(false);\n    setTimeout(() => {\n      advanceStage();\n      setIsTransitioning(false);\n      setPreviousStats(null);\n    }, 1000);\n  };\n\n  // Get completed stages based on choice history\n  function getCompletedStages() {\n    const stages = Object.values(LIFE_STAGES);\n    const currentIndex = stages.indexOf(currentStage);\n    return stages.slice(0, currentIndex);\n  }\n\n  // Get stage summary for completed stage\n  const getStageChoices = () => {\n    return choiceHistory.filter(choice => choice.stage === currentStage);\n  };\n  if (gameComplete) {\n    return null; // Game end component will be handled separately\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"game-flow\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"game-header\",\n      children: /*#__PURE__*/_jsxDEV(TimelineVisualization, {\n        currentStage: currentStage,\n        completedStages: completedStages,\n        animated: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"game-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: [/*#__PURE__*/_jsxDEV(LifeStageDisplay, {\n          currentStage: currentStage,\n          isTransitioning: isTransitioning,\n          onTransitionComplete: () => setIsTransitioning(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), currentChoice && !showStageComplete && /*#__PURE__*/_jsxDEV(ChoicePresentation, {\n          choice: currentChoice,\n          onChoiceSelect: handleChoiceSelect,\n          showEffects: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this), showStageComplete && /*#__PURE__*/_jsxDEV(StageCompleteCard, {\n          stage: currentStage,\n          choices: getStageChoices(),\n          onAdvance: handleAdvanceStage,\n          canAdvance: canAdvanceStage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar\",\n        children: /*#__PURE__*/_jsxDEV(StatsDashboard, {\n          stats: stats,\n          previousStats: previousStats,\n          showChanges: previousStats !== null,\n          compact: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n\n// Stage complete card component\n_s(GameFlow, \"o2JwVWb3i5T3s0QMEYD7j3tN/S4=\", false, function () {\n  return [useGame];\n});\n_c = GameFlow;\nconst StageCompleteCard = ({\n  stage,\n  choices,\n  onAdvance,\n  canAdvance\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stage-complete-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"complete-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"complete-icon\",\n        children: \"\\uD83C\\uDF89\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"complete-title\",\n        children: \"Stage Complete!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"complete-description\",\n        children: \"You've finished this chapter of your life. Here's what happened:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stage-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Your Choices:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"choices-summary\",\n        children: choices.map((choice, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"choice-summary-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"choice-summary-title\",\n            children: choice.choiceTitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"choice-summary-selected\",\n            children: choice.selectedOption\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"choice-summary-story\",\n            children: choice.story\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), canAdvance && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"advance-controls\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"advance-button\",\n        onClick: onAdvance,\n        children: \"Continue to Next Stage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_c2 = StageCompleteCard;\nexport default GameFlow;\nvar _c, _c2;\n$RefreshReg$(_c, \"GameFlow\");\n$RefreshReg$(_c2, \"StageCompleteCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useGame", "LifeStageDisplay", "TimelineVisualization", "StatsDashboard", "ChoicePresentation", "LIFE_STAGES", "jsxDEV", "_jsxDEV", "GameFlow", "_s", "currentStage", "stats", "choiceHistory", "gameComplete", "getCurrentChoice", "hasMoreChoicesInStage", "canAdvanceStage", "makeChoice", "advanceStage", "isStageComplete", "isTransitioning", "setIsTransitioning", "previousStats", "setPreviousStats", "showStageComplete", "setShowStageComplete", "currentChoice", "completedStages", "getCompletedStages", "handleChoiceSelect", "choice", "option", "setTimeout", "handleAdvanceStage", "stages", "Object", "values", "currentIndex", "indexOf", "slice", "getStageChoices", "filter", "stage", "className", "children", "animated", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onTransitionComplete", "onChoiceSelect", "showEffects", "StageCompleteCard", "choices", "onAdvance", "canAdvance", "showChanges", "compact", "_c", "map", "index", "choiceTitle", "selectedOption", "story", "onClick", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/components/GameFlow.js"], "sourcesContent": ["// Main game flow component\n\nimport React, { useState, useEffect } from 'react';\nimport { useGame } from '../contexts/GameContext';\nimport LifeStageDisplay from './LifeStageDisplay';\nimport TimelineVisualization from './TimelineVisualization';\nimport StatsDashboard from './StatsDashboard';\nimport ChoicePresentation from './ChoicePresentation';\nimport { LIFE_STAGES } from '../data/gameData';\nimport '../styles/GameFlow.css';\n\nconst GameFlow = () => {\n  const {\n    currentStage,\n    stats,\n    choiceHistory,\n    gameComplete,\n    getCurrentChoice,\n    hasMoreChoicesInStage,\n    canAdvanceStage,\n    makeChoice,\n    advanceStage,\n    isStageComplete\n  } = useGame();\n\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [previousStats, setPreviousStats] = useState(null);\n  const [showStageComplete, setShowStageComplete] = useState(false);\n\n  const currentChoice = getCurrentChoice();\n  const completedStages = getCompletedStages();\n\n  // Handle choice selection\n  const handleChoiceSelect = (choice, option) => {\n    setPreviousStats({ ...stats });\n    makeChoice(choice, option);\n    \n    // Check if stage is complete after this choice\n    setTimeout(() => {\n      if (!hasMoreChoicesInStage()) {\n        setShowStageComplete(true);\n      }\n    }, 1000);\n  };\n\n  // Handle stage advancement\n  const handleAdvanceStage = () => {\n    setIsTransitioning(true);\n    setShowStageComplete(false);\n    \n    setTimeout(() => {\n      advanceStage();\n      setIsTransitioning(false);\n      setPreviousStats(null);\n    }, 1000);\n  };\n\n  // Get completed stages based on choice history\n  function getCompletedStages() {\n    const stages = Object.values(LIFE_STAGES);\n    const currentIndex = stages.indexOf(currentStage);\n    return stages.slice(0, currentIndex);\n  }\n\n  // Get stage summary for completed stage\n  const getStageChoices = () => {\n    return choiceHistory.filter(choice => choice.stage === currentStage);\n  };\n\n  if (gameComplete) {\n    return null; // Game end component will be handled separately\n  }\n\n  return (\n    <div className=\"game-flow\">\n      <div className=\"game-header\">\n        <TimelineVisualization \n          currentStage={currentStage}\n          completedStages={completedStages}\n          animated={true}\n        />\n      </div>\n\n      <div className=\"game-content\">\n        <div className=\"main-content\">\n          <LifeStageDisplay \n            currentStage={currentStage}\n            isTransitioning={isTransitioning}\n            onTransitionComplete={() => setIsTransitioning(false)}\n          />\n\n          {currentChoice && !showStageComplete && (\n            <ChoicePresentation\n              choice={currentChoice}\n              onChoiceSelect={handleChoiceSelect}\n              showEffects={true}\n            />\n          )}\n\n          {showStageComplete && (\n            <StageCompleteCard \n              stage={currentStage}\n              choices={getStageChoices()}\n              onAdvance={handleAdvanceStage}\n              canAdvance={canAdvanceStage}\n            />\n          )}\n        </div>\n\n        <div className=\"sidebar\">\n          <StatsDashboard \n            stats={stats}\n            previousStats={previousStats}\n            showChanges={previousStats !== null}\n            compact={false}\n          />\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Stage complete card component\nconst StageCompleteCard = ({ stage, choices, onAdvance, canAdvance }) => {\n  return (\n    <div className=\"stage-complete-card\">\n      <div className=\"complete-header\">\n        <div className=\"complete-icon\">🎉</div>\n        <h2 className=\"complete-title\">Stage Complete!</h2>\n        <p className=\"complete-description\">\n          You've finished this chapter of your life. Here's what happened:\n        </p>\n      </div>\n\n      <div className=\"stage-summary\">\n        <h3>Your Choices:</h3>\n        <div className=\"choices-summary\">\n          {choices.map((choice, index) => (\n            <div key={index} className=\"choice-summary-item\">\n              <div className=\"choice-summary-title\">{choice.choiceTitle}</div>\n              <div className=\"choice-summary-selected\">{choice.selectedOption}</div>\n              <div className=\"choice-summary-story\">{choice.story}</div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {canAdvance && (\n        <div className=\"advance-controls\">\n          <button \n            className=\"advance-button\"\n            onClick={onAdvance}\n          >\n            Continue to Next Stage\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default GameFlow;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IACJC,YAAY;IACZC,KAAK;IACLC,aAAa;IACbC,YAAY;IACZC,gBAAgB;IAChBC,qBAAqB;IACrBC,eAAe;IACfC,UAAU;IACVC,YAAY;IACZC;EACF,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAEb,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC0B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAM4B,aAAa,GAAGZ,gBAAgB,CAAC,CAAC;EACxC,MAAMa,eAAe,GAAGC,kBAAkB,CAAC,CAAC;;EAE5C;EACA,MAAMC,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IAC7CR,gBAAgB,CAAC;MAAE,GAAGZ;IAAM,CAAC,CAAC;IAC9BM,UAAU,CAACa,MAAM,EAAEC,MAAM,CAAC;;IAE1B;IACAC,UAAU,CAAC,MAAM;MACf,IAAI,CAACjB,qBAAqB,CAAC,CAAC,EAAE;QAC5BU,oBAAoB,CAAC,IAAI,CAAC;MAC5B;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM;IAC/BZ,kBAAkB,CAAC,IAAI,CAAC;IACxBI,oBAAoB,CAAC,KAAK,CAAC;IAE3BO,UAAU,CAAC,MAAM;MACfd,YAAY,CAAC,CAAC;MACdG,kBAAkB,CAAC,KAAK,CAAC;MACzBE,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,SAASK,kBAAkBA,CAAA,EAAG;IAC5B,MAAMM,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC/B,WAAW,CAAC;IACzC,MAAMgC,YAAY,GAAGH,MAAM,CAACI,OAAO,CAAC5B,YAAY,CAAC;IACjD,OAAOwB,MAAM,CAACK,KAAK,CAAC,CAAC,EAAEF,YAAY,CAAC;EACtC;;EAEA;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO5B,aAAa,CAAC6B,MAAM,CAACX,MAAM,IAAIA,MAAM,CAACY,KAAK,KAAKhC,YAAY,CAAC;EACtE,CAAC;EAED,IAAIG,YAAY,EAAE;IAChB,OAAO,IAAI,CAAC,CAAC;EACf;EAEA,oBACEN,OAAA;IAAKoC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBrC,OAAA;MAAKoC,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BrC,OAAA,CAACL,qBAAqB;QACpBQ,YAAY,EAAEA,YAAa;QAC3BiB,eAAe,EAAEA,eAAgB;QACjCkB,QAAQ,EAAE;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN1C,OAAA;MAAKoC,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BrC,OAAA;QAAKoC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BrC,OAAA,CAACN,gBAAgB;UACfS,YAAY,EAAEA,YAAa;UAC3BU,eAAe,EAAEA,eAAgB;UACjC8B,oBAAoB,EAAEA,CAAA,KAAM7B,kBAAkB,CAAC,KAAK;QAAE;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,EAEDvB,aAAa,IAAI,CAACF,iBAAiB,iBAClCjB,OAAA,CAACH,kBAAkB;UACjB0B,MAAM,EAAEJ,aAAc;UACtByB,cAAc,EAAEtB,kBAAmB;UACnCuB,WAAW,EAAE;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACF,EAEAzB,iBAAiB,iBAChBjB,OAAA,CAAC8C,iBAAiB;UAChBX,KAAK,EAAEhC,YAAa;UACpB4C,OAAO,EAAEd,eAAe,CAAC,CAAE;UAC3Be,SAAS,EAAEtB,kBAAmB;UAC9BuB,UAAU,EAAExC;QAAgB;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1C,OAAA;QAAKoC,SAAS,EAAC,SAAS;QAAAC,QAAA,eACtBrC,OAAA,CAACJ,cAAc;UACbQ,KAAK,EAAEA,KAAM;UACbW,aAAa,EAAEA,aAAc;UAC7BmC,WAAW,EAAEnC,aAAa,KAAK,IAAK;UACpCoC,OAAO,EAAE;QAAM;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAxC,EAAA,CA/GMD,QAAQ;EAAA,QAYRR,OAAO;AAAA;AAAA2D,EAAA,GAZPnD,QAAQ;AAgHd,MAAM6C,iBAAiB,GAAGA,CAAC;EAAEX,KAAK;EAAEY,OAAO;EAAEC,SAAS;EAAEC;AAAW,CAAC,KAAK;EACvE,oBACEjD,OAAA;IAAKoC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCrC,OAAA;MAAKoC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BrC,OAAA;QAAKoC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvC1C,OAAA;QAAIoC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnD1C,OAAA;QAAGoC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAEpC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEN1C,OAAA;MAAKoC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BrC,OAAA;QAAAqC,QAAA,EAAI;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB1C,OAAA;QAAKoC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7BU,OAAO,CAACM,GAAG,CAAC,CAAC9B,MAAM,EAAE+B,KAAK,kBACzBtD,OAAA;UAAiBoC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAC9CrC,OAAA;YAAKoC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEd,MAAM,CAACgC;UAAW;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChE1C,OAAA;YAAKoC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAEd,MAAM,CAACiC;UAAc;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtE1C,OAAA;YAAKoC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEd,MAAM,CAACkC;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAHlDY,KAAK;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELO,UAAU,iBACTjD,OAAA;MAAKoC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BrC,OAAA;QACEoC,SAAS,EAAC,gBAAgB;QAC1BsB,OAAO,EAAEV,SAAU;QAAAX,QAAA,EACpB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACiB,GAAA,GApCIb,iBAAiB;AAsCvB,eAAe7C,QAAQ;AAAC,IAAAmD,EAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}