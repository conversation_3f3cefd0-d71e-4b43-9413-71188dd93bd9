{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LTS\\\\life-timeline-simulator\\\\src\\\\contexts\\\\GameContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n// Game Context for managing global state\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { LIFE_STAGES, INITIAL_STATS, LIFE_CHOICES } from '../data/gameData';\nimport { applyStatEffects, getNextStage, saveGameState, loadGameState, clearGameState } from '../utils/gameUtils';\n\n// Action types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GAME_ACTIONS = {\n  START_NEW_GAME: 'START_NEW_GAME',\n  LOAD_GAME: 'LOAD_GAME',\n  MAKE_CHOICE: 'MAKE_CHOICE',\n  ADVANCE_STAGE: 'ADVANCE_STAGE',\n  RESTART_GAME: 'RESTART_GAME',\n  SET_LOADING: 'SET_LOADING'\n};\n\n// Initial game state\nconst initialState = {\n  currentStage: LIFE_STAGES.CHILDHOOD,\n  currentChoiceIndex: 0,\n  stats: {\n    ...INITIAL_STATS\n  },\n  choiceHistory: [],\n  gameComplete: false,\n  loading: false,\n  gameStarted: false\n};\n\n// Game reducer\nconst gameReducer = (state, action) => {\n  switch (action.type) {\n    case GAME_ACTIONS.START_NEW_GAME:\n      return {\n        ...initialState,\n        gameStarted: true,\n        loading: false\n      };\n    case GAME_ACTIONS.LOAD_GAME:\n      return {\n        ...action.payload,\n        loading: false\n      };\n    case GAME_ACTIONS.MAKE_CHOICE:\n      const {\n        choice,\n        option\n      } = action.payload;\n      const newStats = applyStatEffects(state.stats, option.effects);\n      const newChoiceHistory = [...state.choiceHistory, {\n        stage: state.currentStage,\n        choiceId: choice.id,\n        choiceTitle: choice.title,\n        selectedOption: option.text,\n        effects: option.effects,\n        story: option.story,\n        timestamp: new Date().toISOString()\n      }];\n      return {\n        ...state,\n        stats: newStats,\n        choiceHistory: newChoiceHistory,\n        currentChoiceIndex: state.currentChoiceIndex + 1\n      };\n    case GAME_ACTIONS.ADVANCE_STAGE:\n      const nextStage = getNextStage(state.currentStage);\n      return {\n        ...state,\n        currentStage: nextStage || state.currentStage,\n        currentChoiceIndex: 0,\n        gameComplete: nextStage === null\n      };\n    case GAME_ACTIONS.RESTART_GAME:\n      return {\n        ...initialState,\n        gameStarted: true\n      };\n    case GAME_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst GameContext = /*#__PURE__*/createContext();\n\n// Custom hook to use game context\nexport const useGame = () => {\n  _s();\n  const context = useContext(GameContext);\n  if (!context) {\n    throw new Error('useGame must be used within a GameProvider');\n  }\n  return context;\n};\n\n// Game provider component\n_s(useGame, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const GameProvider = ({\n  children\n}) => {\n  _s2();\n  const [state, dispatch] = useReducer(gameReducer, initialState);\n\n  // Auto-save game state when it changes\n  useEffect(() => {\n    if (state.gameStarted && !state.loading) {\n      saveGameState(state);\n    }\n  }, [state]);\n\n  // Game actions\n  const startNewGame = () => {\n    clearGameState();\n    dispatch({\n      type: GAME_ACTIONS.START_NEW_GAME\n    });\n  };\n  const loadGame = () => {\n    dispatch({\n      type: GAME_ACTIONS.SET_LOADING,\n      payload: true\n    });\n    const savedState = loadGameState();\n    if (savedState) {\n      dispatch({\n        type: GAME_ACTIONS.LOAD_GAME,\n        payload: savedState\n      });\n    } else {\n      dispatch({\n        type: GAME_ACTIONS.START_NEW_GAME\n      });\n    }\n  };\n  const makeChoice = (choice, option) => {\n    dispatch({\n      type: GAME_ACTIONS.MAKE_CHOICE,\n      payload: {\n        choice,\n        option\n      }\n    });\n  };\n  const advanceStage = () => {\n    dispatch({\n      type: GAME_ACTIONS.ADVANCE_STAGE\n    });\n  };\n  const restartGame = () => {\n    clearGameState();\n    dispatch({\n      type: GAME_ACTIONS.RESTART_GAME\n    });\n  };\n\n  // Get current choices for the current stage\n  const getCurrentChoices = () => {\n    const stageChoices = LIFE_CHOICES[state.currentStage] || [];\n    return stageChoices;\n  };\n\n  // Get current choice (if any)\n  const getCurrentChoice = () => {\n    const choices = getCurrentChoices();\n    return choices[state.currentChoiceIndex] || null;\n  };\n\n  // Check if current stage has more choices\n  const hasMoreChoicesInStage = () => {\n    const choices = getCurrentChoices();\n    return state.currentChoiceIndex < choices.length;\n  };\n\n  // Check if there's a saved game\n  const hasSavedGame = () => {\n    return loadGameState() !== null;\n  };\n\n  // Get life story from choice history\n  const getLifeStory = () => {\n    return state.choiceHistory.map(choice => ({\n      stage: choice.stage,\n      story: choice.story,\n      choice: choice.selectedOption\n    }));\n  };\n\n  // Context value\n  const value = {\n    // State\n    ...state,\n    // Actions\n    startNewGame,\n    loadGame,\n    makeChoice,\n    advanceStage,\n    restartGame,\n    // Getters\n    getCurrentChoices,\n    getCurrentChoice,\n    hasMoreChoicesInStage,\n    hasSavedGame,\n    getLifeStory,\n    // Computed values\n    isStageComplete: !hasMoreChoicesInStage(),\n    canAdvanceStage: !hasMoreChoicesInStage() && !state.gameComplete\n  };\n  return /*#__PURE__*/_jsxDEV(GameContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n};\n_s2(GameProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = GameProvider;\nvar _c;\n$RefreshReg$(_c, \"GameProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "LIFE_STAGES", "INITIAL_STATS", "LIFE_CHOICES", "applyStatEffects", "getNextStage", "saveGameState", "loadGameState", "clearGameState", "jsxDEV", "_jsxDEV", "GAME_ACTIONS", "START_NEW_GAME", "LOAD_GAME", "MAKE_CHOICE", "ADVANCE_STAGE", "RESTART_GAME", "SET_LOADING", "initialState", "currentStage", "CHILDHOOD", "currentChoiceIndex", "stats", "choiceHistory", "gameComplete", "loading", "gameStarted", "gameReducer", "state", "action", "type", "payload", "choice", "option", "newStats", "effects", "newChoiceHistory", "stage", "choiceId", "id", "choiceTitle", "title", "selectedOption", "text", "story", "timestamp", "Date", "toISOString", "nextStage", "GameContext", "useGame", "_s", "context", "Error", "GameProvider", "children", "_s2", "dispatch", "startNewGame", "loadGame", "savedState", "makeChoice", "advanceStage", "restartGame", "getCurrentChoices", "stageChoices", "getCurrentChoice", "choices", "hasMoreChoicesInStage", "length", "hasSavedGame", "getLifeStory", "map", "value", "isStageComplete", "canAdvanceStage", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/contexts/GameContext.js"], "sourcesContent": ["// Game Context for managing global state\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { \n  LIFE_STAGES, \n  INITIAL_STATS, \n  LIFE_CHOICES \n} from '../data/gameData';\nimport { \n  applyStatEffects, \n  getNextStage, \n  saveGameState, \n  loadGameState,\n  clearGameState \n} from '../utils/gameUtils';\n\n// Action types\nconst GAME_ACTIONS = {\n  START_NEW_GAME: 'START_NEW_GAME',\n  LOAD_GAME: 'LOAD_GAME',\n  MAKE_CHOICE: 'MAKE_CHOICE',\n  ADVANCE_STAGE: 'ADVANCE_STAGE',\n  RESTART_GAME: 'RESTART_GAME',\n  SET_LOADING: 'SET_LOADING'\n};\n\n// Initial game state\nconst initialState = {\n  currentStage: LIFE_STAGES.CHILDHOOD,\n  currentChoiceIndex: 0,\n  stats: { ...INITIAL_STATS },\n  choiceHistory: [],\n  gameComplete: false,\n  loading: false,\n  gameStarted: false\n};\n\n// Game reducer\nconst gameReducer = (state, action) => {\n  switch (action.type) {\n    case GAME_ACTIONS.START_NEW_GAME:\n      return {\n        ...initialState,\n        gameStarted: true,\n        loading: false\n      };\n\n    case GAME_ACTIONS.LOAD_GAME:\n      return {\n        ...action.payload,\n        loading: false\n      };\n\n    case GAME_ACTIONS.MAKE_CHOICE:\n      const { choice, option } = action.payload;\n      const newStats = applyStatEffects(state.stats, option.effects);\n      \n      const newChoiceHistory = [\n        ...state.choiceHistory,\n        {\n          stage: state.currentStage,\n          choiceId: choice.id,\n          choiceTitle: choice.title,\n          selectedOption: option.text,\n          effects: option.effects,\n          story: option.story,\n          timestamp: new Date().toISOString()\n        }\n      ];\n\n      return {\n        ...state,\n        stats: newStats,\n        choiceHistory: newChoiceHistory,\n        currentChoiceIndex: state.currentChoiceIndex + 1\n      };\n\n    case GAME_ACTIONS.ADVANCE_STAGE:\n      const nextStage = getNextStage(state.currentStage);\n      \n      return {\n        ...state,\n        currentStage: nextStage || state.currentStage,\n        currentChoiceIndex: 0,\n        gameComplete: nextStage === null\n      };\n\n    case GAME_ACTIONS.RESTART_GAME:\n      return {\n        ...initialState,\n        gameStarted: true\n      };\n\n    case GAME_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        loading: action.payload\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst GameContext = createContext();\n\n// Custom hook to use game context\nexport const useGame = () => {\n  const context = useContext(GameContext);\n  if (!context) {\n    throw new Error('useGame must be used within a GameProvider');\n  }\n  return context;\n};\n\n// Game provider component\nexport const GameProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(gameReducer, initialState);\n\n  // Auto-save game state when it changes\n  useEffect(() => {\n    if (state.gameStarted && !state.loading) {\n      saveGameState(state);\n    }\n  }, [state]);\n\n  // Game actions\n  const startNewGame = () => {\n    clearGameState();\n    dispatch({ type: GAME_ACTIONS.START_NEW_GAME });\n  };\n\n  const loadGame = () => {\n    dispatch({ type: GAME_ACTIONS.SET_LOADING, payload: true });\n    \n    const savedState = loadGameState();\n    if (savedState) {\n      dispatch({ type: GAME_ACTIONS.LOAD_GAME, payload: savedState });\n    } else {\n      dispatch({ type: GAME_ACTIONS.START_NEW_GAME });\n    }\n  };\n\n  const makeChoice = (choice, option) => {\n    dispatch({ \n      type: GAME_ACTIONS.MAKE_CHOICE, \n      payload: { choice, option } \n    });\n  };\n\n  const advanceStage = () => {\n    dispatch({ type: GAME_ACTIONS.ADVANCE_STAGE });\n  };\n\n  const restartGame = () => {\n    clearGameState();\n    dispatch({ type: GAME_ACTIONS.RESTART_GAME });\n  };\n\n  // Get current choices for the current stage\n  const getCurrentChoices = () => {\n    const stageChoices = LIFE_CHOICES[state.currentStage] || [];\n    return stageChoices;\n  };\n\n  // Get current choice (if any)\n  const getCurrentChoice = () => {\n    const choices = getCurrentChoices();\n    return choices[state.currentChoiceIndex] || null;\n  };\n\n  // Check if current stage has more choices\n  const hasMoreChoicesInStage = () => {\n    const choices = getCurrentChoices();\n    return state.currentChoiceIndex < choices.length;\n  };\n\n  // Check if there's a saved game\n  const hasSavedGame = () => {\n    return loadGameState() !== null;\n  };\n\n  // Get life story from choice history\n  const getLifeStory = () => {\n    return state.choiceHistory.map(choice => ({\n      stage: choice.stage,\n      story: choice.story,\n      choice: choice.selectedOption\n    }));\n  };\n\n  // Context value\n  const value = {\n    // State\n    ...state,\n    \n    // Actions\n    startNewGame,\n    loadGame,\n    makeChoice,\n    advanceStage,\n    restartGame,\n    \n    // Getters\n    getCurrentChoices,\n    getCurrentChoice,\n    hasMoreChoicesInStage,\n    hasSavedGame,\n    getLifeStory,\n    \n    // Computed values\n    isStageComplete: !hasMoreChoicesInStage(),\n    canAdvanceStage: !hasMoreChoicesInStage() && !state.gameComplete\n  };\n\n  return (\n    <GameContext.Provider value={value}>\n      {children}\n    </GameContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA;;AAEA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SACEC,WAAW,EACXC,aAAa,EACbC,YAAY,QACP,kBAAkB;AACzB,SACEC,gBAAgB,EAChBC,YAAY,EACZC,aAAa,EACbC,aAAa,EACbC,cAAc,QACT,oBAAoB;;AAE3B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,cAAc,EAAE,gBAAgB;EAChCC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,YAAY,EAAE,cAAc;EAC5BC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAElB,WAAW,CAACmB,SAAS;EACnCC,kBAAkB,EAAE,CAAC;EACrBC,KAAK,EAAE;IAAE,GAAGpB;EAAc,CAAC;EAC3BqB,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,KAAK;EACnBC,OAAO,EAAE,KAAK;EACdC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKnB,YAAY,CAACC,cAAc;MAC9B,OAAO;QACL,GAAGM,YAAY;QACfQ,WAAW,EAAE,IAAI;QACjBD,OAAO,EAAE;MACX,CAAC;IAEH,KAAKd,YAAY,CAACE,SAAS;MACzB,OAAO;QACL,GAAGgB,MAAM,CAACE,OAAO;QACjBN,OAAO,EAAE;MACX,CAAC;IAEH,KAAKd,YAAY,CAACG,WAAW;MAC3B,MAAM;QAAEkB,MAAM;QAAEC;MAAO,CAAC,GAAGJ,MAAM,CAACE,OAAO;MACzC,MAAMG,QAAQ,GAAG9B,gBAAgB,CAACwB,KAAK,CAACN,KAAK,EAAEW,MAAM,CAACE,OAAO,CAAC;MAE9D,MAAMC,gBAAgB,GAAG,CACvB,GAAGR,KAAK,CAACL,aAAa,EACtB;QACEc,KAAK,EAAET,KAAK,CAACT,YAAY;QACzBmB,QAAQ,EAAEN,MAAM,CAACO,EAAE;QACnBC,WAAW,EAAER,MAAM,CAACS,KAAK;QACzBC,cAAc,EAAET,MAAM,CAACU,IAAI;QAC3BR,OAAO,EAAEF,MAAM,CAACE,OAAO;QACvBS,KAAK,EAAEX,MAAM,CAACW,KAAK;QACnBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CACF;MAED,OAAO;QACL,GAAGnB,KAAK;QACRN,KAAK,EAAEY,QAAQ;QACfX,aAAa,EAAEa,gBAAgB;QAC/Bf,kBAAkB,EAAEO,KAAK,CAACP,kBAAkB,GAAG;MACjD,CAAC;IAEH,KAAKV,YAAY,CAACI,aAAa;MAC7B,MAAMiC,SAAS,GAAG3C,YAAY,CAACuB,KAAK,CAACT,YAAY,CAAC;MAElD,OAAO;QACL,GAAGS,KAAK;QACRT,YAAY,EAAE6B,SAAS,IAAIpB,KAAK,CAACT,YAAY;QAC7CE,kBAAkB,EAAE,CAAC;QACrBG,YAAY,EAAEwB,SAAS,KAAK;MAC9B,CAAC;IAEH,KAAKrC,YAAY,CAACK,YAAY;MAC5B,OAAO;QACL,GAAGE,YAAY;QACfQ,WAAW,EAAE;MACf,CAAC;IAEH,KAAKf,YAAY,CAACM,WAAW;MAC3B,OAAO;QACL,GAAGW,KAAK;QACRH,OAAO,EAAEI,MAAM,CAACE;MAClB,CAAC;IAEH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMqB,WAAW,gBAAGpD,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMqD,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGtD,UAAU,CAACmD,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,OAAO;AASpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAAC5B,KAAK,EAAE6B,QAAQ,CAAC,GAAG1D,UAAU,CAAC4B,WAAW,EAAET,YAAY,CAAC;;EAE/D;EACAlB,SAAS,CAAC,MAAM;IACd,IAAI4B,KAAK,CAACF,WAAW,IAAI,CAACE,KAAK,CAACH,OAAO,EAAE;MACvCnB,aAAa,CAACsB,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEX;EACA,MAAM8B,YAAY,GAAGA,CAAA,KAAM;IACzBlD,cAAc,CAAC,CAAC;IAChBiD,QAAQ,CAAC;MAAE3B,IAAI,EAAEnB,YAAY,CAACC;IAAe,CAAC,CAAC;EACjD,CAAC;EAED,MAAM+C,QAAQ,GAAGA,CAAA,KAAM;IACrBF,QAAQ,CAAC;MAAE3B,IAAI,EAAEnB,YAAY,CAACM,WAAW;MAAEc,OAAO,EAAE;IAAK,CAAC,CAAC;IAE3D,MAAM6B,UAAU,GAAGrD,aAAa,CAAC,CAAC;IAClC,IAAIqD,UAAU,EAAE;MACdH,QAAQ,CAAC;QAAE3B,IAAI,EAAEnB,YAAY,CAACE,SAAS;QAAEkB,OAAO,EAAE6B;MAAW,CAAC,CAAC;IACjE,CAAC,MAAM;MACLH,QAAQ,CAAC;QAAE3B,IAAI,EAAEnB,YAAY,CAACC;MAAe,CAAC,CAAC;IACjD;EACF,CAAC;EAED,MAAMiD,UAAU,GAAGA,CAAC7B,MAAM,EAAEC,MAAM,KAAK;IACrCwB,QAAQ,CAAC;MACP3B,IAAI,EAAEnB,YAAY,CAACG,WAAW;MAC9BiB,OAAO,EAAE;QAAEC,MAAM;QAAEC;MAAO;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzBL,QAAQ,CAAC;MAAE3B,IAAI,EAAEnB,YAAY,CAACI;IAAc,CAAC,CAAC;EAChD,CAAC;EAED,MAAMgD,WAAW,GAAGA,CAAA,KAAM;IACxBvD,cAAc,CAAC,CAAC;IAChBiD,QAAQ,CAAC;MAAE3B,IAAI,EAAEnB,YAAY,CAACK;IAAa,CAAC,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMgD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,YAAY,GAAG9D,YAAY,CAACyB,KAAK,CAACT,YAAY,CAAC,IAAI,EAAE;IAC3D,OAAO8C,YAAY;EACrB,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,OAAO,GAAGH,iBAAiB,CAAC,CAAC;IACnC,OAAOG,OAAO,CAACvC,KAAK,CAACP,kBAAkB,CAAC,IAAI,IAAI;EAClD,CAAC;;EAED;EACA,MAAM+C,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMD,OAAO,GAAGH,iBAAiB,CAAC,CAAC;IACnC,OAAOpC,KAAK,CAACP,kBAAkB,GAAG8C,OAAO,CAACE,MAAM;EAClD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAO/D,aAAa,CAAC,CAAC,KAAK,IAAI;EACjC,CAAC;;EAED;EACA,MAAMgE,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAO3C,KAAK,CAACL,aAAa,CAACiD,GAAG,CAACxC,MAAM,KAAK;MACxCK,KAAK,EAAEL,MAAM,CAACK,KAAK;MACnBO,KAAK,EAAEZ,MAAM,CAACY,KAAK;MACnBZ,MAAM,EAAEA,MAAM,CAACU;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM+B,KAAK,GAAG;IACZ;IACA,GAAG7C,KAAK;IAER;IACA8B,YAAY;IACZC,QAAQ;IACRE,UAAU;IACVC,YAAY;IACZC,WAAW;IAEX;IACAC,iBAAiB;IACjBE,gBAAgB;IAChBE,qBAAqB;IACrBE,YAAY;IACZC,YAAY;IAEZ;IACAG,eAAe,EAAE,CAACN,qBAAqB,CAAC,CAAC;IACzCO,eAAe,EAAE,CAACP,qBAAqB,CAAC,CAAC,IAAI,CAACxC,KAAK,CAACJ;EACtD,CAAC;EAED,oBACEd,OAAA,CAACuC,WAAW,CAAC2B,QAAQ;IAACH,KAAK,EAAEA,KAAM;IAAAlB,QAAA,EAChCA;EAAQ;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACxB,GAAA,CAxGWF,YAAY;AAAA2B,EAAA,GAAZ3B,YAAY;AAAA,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}