// Individual choice card component

import React, { useState } from 'react';
import { getStatIcon, formatStatName } from '../utils/gameUtils';
import '../styles/ChoiceCard.css';

const ChoiceCard = ({ 
  option, 
  onSelect, 
  disabled = false,
  showEffects = false 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (!disabled) {
      onSelect(option);
    }
  };

  const getEffectColor = (value) => {
    if (value > 0) return '#27ae60';
    if (value < 0) return '#e74c3c';
    return '#95a5a6';
  };

  const getEffectIcon = (value) => {
    if (value > 0) return '↗️';
    if (value < 0) return '↘️';
    return '➡️';
  };

  return (
    <div 
      className={`choice-card ${disabled ? 'disabled' : ''} ${isHovered ? 'hovered' : ''}`}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="choice-content">
        <div className="choice-text">
          {option.text}
        </div>
        
        {showEffects && (
          <div className="choice-effects">
            <div className="effects-header">
              <span className="effects-label">Effects:</span>
            </div>
            <div className="effects-grid">
              {Object.entries(option.effects).map(([stat, value]) => (
                <div 
                  key={stat}
                  className="effect-item"
                  style={{ color: getEffectColor(value) }}
                >
                  <span className="effect-icon">{getStatIcon(stat)}</span>
                  <span className="effect-name">{formatStatName(stat)}</span>
                  <span className="effect-value">
                    {getEffectIcon(value)} {value > 0 ? '+' : ''}{value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
      <div className="choice-hover-overlay">
        <div className="hover-text">Choose this path</div>
      </div>
      
      <div className="choice-ripple" />
    </div>
  );
};

export default ChoiceCard;
