/* ChoicePresentation component styles */

.choice-presentation {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 20px;
  padding: 32px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
  position: relative;
}

.choice-header {
  text-align: center;
  margin-bottom: 32px;
}

.choice-title {
  margin: 0 0 16px 0;
  font-size: 2.2em;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.choice-description {
  margin: 0 0 24px 0;
  font-size: 1.2em;
  color: #666;
  line-height: 1.5;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.choice-timer {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  max-width: 300px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.timer-icon {
  font-size: 1.2em;
}

.timer-text {
  font-weight: 600;
  color: #2c3e50;
}

.timer-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4px;
  background: #3498db;
  transition: width 1s linear, background-color 0.3s ease;
  border-radius: 0 0 12px 12px;
}

.choices-container {
  margin-bottom: 32px;
}

.choices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  animation: choicesSlideIn 0.8s ease-out;
}

@keyframes choicesSlideIn {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.choice-feedback {
  background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.05));
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(39, 174, 96, 0.2);
  margin-top: 24px;
  animation: feedbackSlideIn 0.6s ease-out;
}

@keyframes feedbackSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.feedback-content {
  text-align: center;
}

.feedback-icon {
  font-size: 2em;
  margin-bottom: 12px;
  animation: iconSpin 0.8s ease-out;
}

@keyframes iconSpin {
  from {
    transform: rotate(-180deg) scale(0);
  }
  to {
    transform: rotate(0deg) scale(1);
  }
}

.feedback-text {
  font-size: 1.1em;
  font-weight: 600;
  color: #27ae60;
  margin-bottom: 12px;
}

.feedback-story {
  font-size: 1em;
  color: #2c3e50;
  font-style: italic;
  line-height: 1.4;
}

.choice-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(102, 126, 234, 0.2);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.1em;
  font-weight: 600;
  color: #667eea;
}

/* Responsive design */
@media (max-width: 768px) {
  .choice-presentation {
    padding: 24px;
    margin-bottom: 24px;
  }
  
  .choice-header {
    margin-bottom: 24px;
  }
  
  .choice-title {
    font-size: 1.8em;
  }
  
  .choice-description {
    font-size: 1.1em;
  }
  
  .choices-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .choice-timer {
    padding: 12px;
    max-width: 250px;
  }
  
  .timer-text {
    font-size: 0.9em;
  }
  
  .choice-feedback {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .choice-presentation {
    padding: 20px;
    border-radius: 16px;
  }
  
  .choice-title {
    font-size: 1.6em;
  }
  
  .choice-description {
    font-size: 1em;
  }
  
  .choices-grid {
    gap: 12px;
  }
  
  .choice-timer {
    padding: 10px;
    gap: 8px;
  }
  
  .timer-icon {
    font-size: 1em;
  }
  
  .timer-text {
    font-size: 0.85em;
  }
  
  .choice-feedback {
    padding: 16px;
  }
  
  .feedback-icon {
    font-size: 1.5em;
  }
  
  .feedback-text {
    font-size: 1em;
  }
  
  .feedback-story {
    font-size: 0.9em;
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
  }
  
  .loading-text {
    font-size: 1em;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .choice-presentation {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .choice-title {
    color: #ecf0f1;
  }
  
  .choice-description {
    color: #bdc3c7;
  }
  
  .choice-timer {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .timer-text {
    color: #ecf0f1;
  }
  
  .choice-feedback {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.2), rgba(46, 204, 113, 0.1));
    border: 1px solid rgba(39, 174, 96, 0.3);
  }
  
  .feedback-story {
    color: #ecf0f1;
  }
  
  .choice-loading {
    background: rgba(0, 0, 0, 0.9);
  }
  
  .loading-text {
    color: #a8b8f0;
  }
}
