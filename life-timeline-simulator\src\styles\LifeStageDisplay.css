/* LifeStageDisplay component styles */

.life-stage-display {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  border-radius: 20px;
  padding: 32px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
  transition: all 0.3s ease;
}

.life-stage-display:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 50px rgba(0, 0, 0, 0.15);
}

.life-stage-display.stage-transition {
  animation: stageTransition 1s ease-in-out;
}

@keyframes stageTransition {
  0% {
    transform: scale(1) rotateY(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(0.95) rotateY(90deg);
    opacity: 0.7;
  }
  100% {
    transform: scale(1) rotateY(0deg);
    opacity: 1;
  }
}

.stage-header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
}

.stage-icon-container {
  position: relative;
}

.stage-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5em;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.stage-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  border-radius: 50%;
}

.stage-icon::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 3s infinite;
  border-radius: 50%;
}

@keyframes shimmer {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.stage-info {
  flex: 1;
}

.stage-name {
  margin: 0 0 8px 0;
  font-size: 2.2em;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.stage-description {
  margin: 0 0 12px 0;
  font-size: 1.1em;
  color: #666;
  line-height: 1.4;
}

.stage-age {
  display: inline-block;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9em;
  font-weight: 600;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.stage-progress {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-label {
  font-size: 0.9em;
  color: #666;
  margin-bottom: 8px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.progress-bar {
  height: 12px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  transition: width 1s ease-in-out;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.progress-text {
  text-align: center;
  font-size: 0.85em;
  color: #666;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .life-stage-display {
    padding: 24px;
    margin-bottom: 24px;
  }
  
  .stage-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .stage-icon {
    width: 70px;
    height: 70px;
    font-size: 2.2em;
  }
  
  .stage-name {
    font-size: 1.8em;
  }
  
  .stage-description {
    font-size: 1em;
  }
  
  .stage-progress {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .life-stage-display {
    padding: 20px;
    border-radius: 16px;
  }
  
  .stage-icon {
    width: 60px;
    height: 60px;
    font-size: 2em;
  }
  
  .stage-name {
    font-size: 1.6em;
  }
  
  .stage-description {
    font-size: 0.95em;
  }
  
  .stage-age {
    font-size: 0.85em;
    padding: 4px 10px;
  }
  
  .stage-progress {
    padding: 12px;
  }
  
  .progress-bar {
    height: 10px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .life-stage-display {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .stage-name {
    color: #ecf0f1;
  }
  
  .stage-description {
    color: #bdc3c7;
  }
  
  .stage-age {
    background: rgba(102, 126, 234, 0.2);
    color: #a8b8f0;
  }
  
  .stage-progress {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .progress-label {
    color: #bdc3c7;
  }
  
  .progress-text {
    color: #bdc3c7;
  }
}
