// Main menu component

import React from 'react';
import { useGame } from '../contexts/GameContext';
import '../styles/MainMenu.css';

const MainMenu = () => {
  const { startNewGame, loadGame, hasSavedGame } = useGame();

  return (
    <div className="main-menu">
      <div className="menu-background">
        <div className="floating-icons">
          <span className="floating-icon">🧒</span>
          <span className="floating-icon">🧑‍🎓</span>
          <span className="floating-icon">👨‍💼</span>
          <span className="floating-icon">👨‍👩‍👧‍👦</span>
          <span className="floating-icon">👴</span>
        </div>
      </div>

      <div className="menu-content">
        <div className="game-title">
          <h1 className="title-main">Life Timeline Simulator</h1>
          <p className="title-subtitle">
            Experience a lifetime of choices and see how they shape your story
          </p>
        </div>

        <div className="menu-buttons">
          <button 
            className="menu-button primary"
            onClick={startNewGame}
          >
            <div className="button-icon">🌟</div>
            <div className="button-text">
              <div className="button-title">Start New Life</div>
              <div className="button-description">Begin your journey from childhood</div>
            </div>
          </button>

          {hasSavedGame() && (
            <button 
              className="menu-button secondary"
              onClick={loadGame}
            >
              <div className="button-icon">📖</div>
              <div className="button-text">
                <div className="button-title">Continue Story</div>
                <div className="button-description">Resume your saved game</div>
              </div>
            </button>
          )}

          <button 
            className="menu-button tertiary"
            onClick={() => window.open('https://github.com', '_blank')}
          >
            <div className="button-icon">ℹ️</div>
            <div className="button-text">
              <div className="button-title">About</div>
              <div className="button-description">Learn about this simulation</div>
            </div>
          </button>
        </div>

        <div className="game-features">
          <div className="feature-item">
            <div className="feature-icon">🎯</div>
            <div className="feature-text">Make meaningful life choices</div>
          </div>
          <div className="feature-item">
            <div className="feature-icon">📊</div>
            <div className="feature-text">Track your life stats</div>
          </div>
          <div className="feature-item">
            <div className="feature-icon">🏆</div>
            <div className="feature-text">See your life story unfold</div>
          </div>
          <div className="feature-item">
            <div className="feature-icon">💾</div>
            <div className="feature-text">Auto-save progress</div>
          </div>
        </div>
      </div>

      <div className="menu-footer">
        <p>Built with React.js • Local Storage • Responsive Design</p>
      </div>
    </div>
  );
};

export default MainMenu;
