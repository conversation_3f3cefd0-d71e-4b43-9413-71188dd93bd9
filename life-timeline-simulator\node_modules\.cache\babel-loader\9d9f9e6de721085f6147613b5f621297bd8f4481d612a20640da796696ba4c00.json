{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LTS\\\\life-timeline-simulator\\\\src\\\\components\\\\StatsDashboard.js\",\n  _s = $RefreshSig$();\n// Main stats dashboard component\n\nimport React, { useState, useEffect } from 'react';\nimport StatBar from './StatBar';\nimport { STATS } from '../data/gameData';\nimport { calculateLifeScore } from '../utils/gameUtils';\nimport '../styles/StatsDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsDashboard = ({\n  stats,\n  previousStats = null,\n  showChanges = false,\n  compact = false\n}) => {\n  _s();\n  const [animationKey, setAnimationKey] = useState(0);\n\n  // Trigger animation when stats change\n  useEffect(() => {\n    if (previousStats) {\n      setAnimationKey(prev => prev + 1);\n    }\n  }, [stats, previousStats]);\n  const overallScore = calculateLifeScore(stats);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `stats-dashboard ${compact ? 'compact' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Life Stats\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overall-score\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"score-label\",\n          children: \"Overall:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"score-value\",\n          children: overallScore\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      children: Object.values(STATS).map(statType => /*#__PURE__*/_jsxDEV(StatBar, {\n        statType: statType,\n        value: stats[statType],\n        previousValue: previousStats ? previousStats[statType] : null,\n        showChange: showChanges,\n        animated: true\n      }, statType, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this))\n    }, animationKey, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), !compact && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"summary-label\",\n          children: \"Strongest Area:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"summary-value\",\n          children: getStrongestStat(stats)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"summary-label\",\n          children: \"Needs Attention:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"summary-value\",\n          children: getWeakestStat(stats)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n\n// Helper function to get strongest stat\n_s(StatsDashboard, \"ixUHHrlViQP0IYMSZdsGDKdNGNc=\");\n_c = StatsDashboard;\nconst getStrongestStat = stats => {\n  const maxStat = Math.max(...Object.values(stats));\n  const strongestStatKey = Object.keys(stats).find(key => stats[key] === maxStat);\n  return strongestStatKey ? formatStatName(strongestStatKey) : 'None';\n};\n\n// Helper function to get weakest stat\nconst getWeakestStat = stats => {\n  const minStat = Math.min(...Object.values(stats));\n  const weakestStatKey = Object.keys(stats).find(key => stats[key] === minStat);\n  return weakestStatKey ? formatStatName(weakestStatKey) : 'None';\n};\n\n// Helper function to format stat name\nconst formatStatName = statType => {\n  const names = {\n    health: 'Health',\n    happiness: 'Happiness',\n    money: 'Money',\n    relationships: 'Relationships'\n  };\n  return names[statType] || statType;\n};\nexport default StatsDashboard;\nvar _c;\n$RefreshReg$(_c, \"StatsDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "StatBar", "STATS", "calculateLifeScore", "jsxDEV", "_jsxDEV", "StatsDashboard", "stats", "previousStats", "showChanges", "compact", "_s", "animationKey", "setAnimationKey", "prev", "overallScore", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Object", "values", "map", "statType", "value", "previousValue", "showChange", "animated", "getStrongestStat", "getWeakestStat", "_c", "maxStat", "Math", "max", "strongestStatKey", "keys", "find", "key", "formatStatName", "minStat", "min", "weakestStatKey", "names", "health", "happiness", "money", "relationships", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/components/StatsDashboard.js"], "sourcesContent": ["// Main stats dashboard component\n\nimport React, { useState, useEffect } from 'react';\nimport StatBar from './StatBar';\nimport { STATS } from '../data/gameData';\nimport { calculateLifeScore } from '../utils/gameUtils';\nimport '../styles/StatsDashboard.css';\n\nconst StatsDashboard = ({ \n  stats, \n  previousStats = null, \n  showChanges = false,\n  compact = false \n}) => {\n  const [animationKey, setAnimationKey] = useState(0);\n  \n  // Trigger animation when stats change\n  useEffect(() => {\n    if (previousStats) {\n      setAnimationKey(prev => prev + 1);\n    }\n  }, [stats, previousStats]);\n\n  const overallScore = calculateLifeScore(stats);\n  \n  return (\n    <div className={`stats-dashboard ${compact ? 'compact' : ''}`}>\n      <div className=\"dashboard-header\">\n        <h3>Life Stats</h3>\n        <div className=\"overall-score\">\n          <span className=\"score-label\">Overall:</span>\n          <span className=\"score-value\">{overallScore}</span>\n        </div>\n      </div>\n      \n      <div className=\"stats-grid\" key={animationKey}>\n        {Object.values(STATS).map(statType => (\n          <StatBar\n            key={statType}\n            statType={statType}\n            value={stats[statType]}\n            previousValue={previousStats ? previousStats[statType] : null}\n            showChange={showChanges}\n            animated={true}\n          />\n        ))}\n      </div>\n      \n      {!compact && (\n        <div className=\"stats-summary\">\n          <div className=\"summary-item\">\n            <span className=\"summary-label\">Strongest Area:</span>\n            <span className=\"summary-value\">{getStrongestStat(stats)}</span>\n          </div>\n          <div className=\"summary-item\">\n            <span className=\"summary-label\">Needs Attention:</span>\n            <span className=\"summary-value\">{getWeakestStat(stats)}</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Helper function to get strongest stat\nconst getStrongestStat = (stats) => {\n  const maxStat = Math.max(...Object.values(stats));\n  const strongestStatKey = Object.keys(stats).find(key => stats[key] === maxStat);\n  return strongestStatKey ? formatStatName(strongestStatKey) : 'None';\n};\n\n// Helper function to get weakest stat\nconst getWeakestStat = (stats) => {\n  const minStat = Math.min(...Object.values(stats));\n  const weakestStatKey = Object.keys(stats).find(key => stats[key] === minStat);\n  return weakestStatKey ? formatStatName(weakestStatKey) : 'None';\n};\n\n// Helper function to format stat name\nconst formatStatName = (statType) => {\n  const names = {\n    health: 'Health',\n    happiness: 'Happiness',\n    money: 'Money',\n    relationships: 'Relationships'\n  };\n  return names[statType] || statType;\n};\n\nexport default StatsDashboard;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,cAAc,GAAGA,CAAC;EACtBC,KAAK;EACLC,aAAa,GAAG,IAAI;EACpBC,WAAW,GAAG,KAAK;EACnBC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIQ,aAAa,EAAE;MACjBK,eAAe,CAACC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACP,KAAK,EAAEC,aAAa,CAAC,CAAC;EAE1B,MAAMO,YAAY,GAAGZ,kBAAkB,CAACI,KAAK,CAAC;EAE9C,oBACEF,OAAA;IAAKW,SAAS,EAAE,mBAAmBN,OAAO,GAAG,SAAS,GAAG,EAAE,EAAG;IAAAO,QAAA,gBAC5DZ,OAAA;MAAKW,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BZ,OAAA;QAAAY,QAAA,EAAI;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnBhB,OAAA;QAAKW,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BZ,OAAA;UAAMW,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7ChB,OAAA;UAAMW,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEF;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhB,OAAA;MAAKW,SAAS,EAAC,YAAY;MAAAC,QAAA,EACxBK,MAAM,CAACC,MAAM,CAACrB,KAAK,CAAC,CAACsB,GAAG,CAACC,QAAQ,iBAChCpB,OAAA,CAACJ,OAAO;QAENwB,QAAQ,EAAEA,QAAS;QACnBC,KAAK,EAAEnB,KAAK,CAACkB,QAAQ,CAAE;QACvBE,aAAa,EAAEnB,aAAa,GAAGA,aAAa,CAACiB,QAAQ,CAAC,GAAG,IAAK;QAC9DG,UAAU,EAAEnB,WAAY;QACxBoB,QAAQ,EAAE;MAAK,GALVJ,QAAQ;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMd,CACF;IAAC,GAV6BT,YAAY;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAWxC,CAAC,EAEL,CAACX,OAAO,iBACPL,OAAA;MAAKW,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BZ,OAAA;QAAKW,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BZ,OAAA;UAAMW,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtDhB,OAAA;UAAMW,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEa,gBAAgB,CAACvB,KAAK;QAAC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACNhB,OAAA;QAAKW,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BZ,OAAA;UAAMW,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvDhB,OAAA;UAAMW,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEc,cAAc,CAACxB,KAAK;QAAC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAV,EAAA,CAxDML,cAAc;AAAA0B,EAAA,GAAd1B,cAAc;AAyDpB,MAAMwB,gBAAgB,GAAIvB,KAAK,IAAK;EAClC,MAAM0B,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGb,MAAM,CAACC,MAAM,CAAChB,KAAK,CAAC,CAAC;EACjD,MAAM6B,gBAAgB,GAAGd,MAAM,CAACe,IAAI,CAAC9B,KAAK,CAAC,CAAC+B,IAAI,CAACC,GAAG,IAAIhC,KAAK,CAACgC,GAAG,CAAC,KAAKN,OAAO,CAAC;EAC/E,OAAOG,gBAAgB,GAAGI,cAAc,CAACJ,gBAAgB,CAAC,GAAG,MAAM;AACrE,CAAC;;AAED;AACA,MAAML,cAAc,GAAIxB,KAAK,IAAK;EAChC,MAAMkC,OAAO,GAAGP,IAAI,CAACQ,GAAG,CAAC,GAAGpB,MAAM,CAACC,MAAM,CAAChB,KAAK,CAAC,CAAC;EACjD,MAAMoC,cAAc,GAAGrB,MAAM,CAACe,IAAI,CAAC9B,KAAK,CAAC,CAAC+B,IAAI,CAACC,GAAG,IAAIhC,KAAK,CAACgC,GAAG,CAAC,KAAKE,OAAO,CAAC;EAC7E,OAAOE,cAAc,GAAGH,cAAc,CAACG,cAAc,CAAC,GAAG,MAAM;AACjE,CAAC;;AAED;AACA,MAAMH,cAAc,GAAIf,QAAQ,IAAK;EACnC,MAAMmB,KAAK,GAAG;IACZC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,WAAW;IACtBC,KAAK,EAAE,OAAO;IACdC,aAAa,EAAE;EACjB,CAAC;EACD,OAAOJ,KAAK,CAACnB,QAAQ,CAAC,IAAIA,QAAQ;AACpC,CAAC;AAED,eAAenB,cAAc;AAAC,IAAA0B,EAAA;AAAAiB,YAAA,CAAAjB,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}