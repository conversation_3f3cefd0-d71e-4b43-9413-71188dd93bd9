[{"C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\GameFlow.js": "4", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\MainMenu.js": "5", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\contexts\\GameContext.js": "6", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\GameEnd.js": "7", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\StatsDashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\ChoicePresentation.js": "9", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\LifeStageDisplay.js": "10", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\TimelineVisualization.js": "11", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\data\\gameData.js": "12", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\utils\\gameUtils.js": "13", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\StatBar.js": "14", "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\ChoiceCard.js": "15"}, {"size": 535, "mtime": 1755712486987, "results": "16", "hashOfConfig": "17"}, {"size": 687, "mtime": 1755713656371, "results": "18", "hashOfConfig": "17"}, {"size": 362, "mtime": 1755712487184, "results": "19", "hashOfConfig": "17"}, {"size": 4592, "mtime": 1755713219748, "results": "20", "hashOfConfig": "17"}, {"size": 3271, "mtime": 1755713255156, "results": "21", "hashOfConfig": "17"}, {"size": 5120, "mtime": 1755712716854, "results": "22", "hashOfConfig": "17"}, {"size": 5573, "mtime": 1755713283200, "results": "23", "hashOfConfig": "17"}, {"size": 2745, "mtime": 1755712780386, "results": "24", "hashOfConfig": "17"}, {"size": 3311, "mtime": 1755712997240, "results": "25", "hashOfConfig": "17"}, {"size": 1865, "mtime": 1755712846581, "results": "26", "hashOfConfig": "17"}, {"size": 2468, "mtime": 1755712859481, "results": "27", "hashOfConfig": "17"}, {"size": 9331, "mtime": 1755712601093, "results": "28", "hashOfConfig": "17"}, {"size": 5632, "mtime": 1755712630539, "results": "29", "hashOfConfig": "17"}, {"size": 2989, "mtime": 1755712761773, "results": "30", "hashOfConfig": "17"}, {"size": 2160, "mtime": 1755712979637, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dqmxdh", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\GameFlow.js", ["77", "78"], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\MainMenu.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\contexts\\GameContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\GameEnd.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\StatsDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\ChoicePresentation.js", ["79"], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\LifeStageDisplay.js", ["80"], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\TimelineVisualization.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\data\\gameData.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\utils\\gameUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\StatBar.js", [], [], "C:\\Users\\<USER>\\Desktop\\LTS\\life-timeline-simulator\\src\\components\\ChoiceCard.js", [], [], {"ruleId": "81", "severity": 1, "message": "82", "line": 3, "column": 27, "nodeType": "83", "messageId": "84", "endLine": 3, "endColumn": 36}, {"ruleId": "81", "severity": 1, "message": "85", "line": 23, "column": 5, "nodeType": "83", "messageId": "84", "endLine": 23, "endColumn": 20}, {"ruleId": "86", "severity": 1, "message": "87", "line": 29, "column": 6, "nodeType": "88", "endLine": 29, "endColumn": 48, "suggestions": "89"}, {"ruleId": "81", "severity": 1, "message": "90", "line": 5, "column": 10, "nodeType": "83", "messageId": "84", "endLine": 5, "endColumn": 21}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'isStageComplete' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleOptionSelect'. Either include it or remove the dependency array.", "ArrayExpression", ["91"], "'LIFE_STAGES' is defined but never used.", {"desc": "92", "fix": "93"}, "Update the dependencies array to be: [timeRemaining, timeLimit, choice.options, handleOptionSelect]", {"range": "94", "text": "95"}, [941, 983], "[timeRemaining, timeLimit, choice.options, handleOptionSelect]"]