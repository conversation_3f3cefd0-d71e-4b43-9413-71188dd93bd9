// Main choice presentation component

import React, { useState, useEffect } from 'react';
import ChoiceCard from './ChoiceCard';
import '../styles/ChoicePresentation.css';

const ChoicePresentation = ({ 
  choice, 
  onChoiceSelect,
  showEffects = true,
  timeLimit = null 
}) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(timeLimit);

  // Timer effect
  useEffect(() => {
    if (timeLimit && timeRemaining > 0) {
      const timer = setTimeout(() => {
        setTimeRemaining(timeRemaining - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeLimit && timeRemaining === 0) {
      // Auto-select random option when time runs out
      const randomOption = choice.options[Math.floor(Math.random() * choice.options.length)];
      handleOptionSelect(randomOption);
    }
  }, [timeRemaining, timeLimit, choice.options]);

  const handleOptionSelect = async (option) => {
    if (isSubmitting) return;
    
    setSelectedOption(option);
    setIsSubmitting(true);
    
    // Add a small delay for visual feedback
    setTimeout(() => {
      onChoiceSelect(choice, option);
      setIsSubmitting(false);
    }, 800);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="choice-presentation">
      <div className="choice-header">
        <h2 className="choice-title">{choice.title}</h2>
        <p className="choice-description">{choice.description}</p>
        
        {timeLimit && timeRemaining > 0 && (
          <div className="choice-timer">
            <div className="timer-icon">⏰</div>
            <div className="timer-text">
              Time remaining: {formatTime(timeRemaining)}
            </div>
            <div 
              className="timer-bar"
              style={{ 
                width: `${(timeRemaining / timeLimit) * 100}%`,
                backgroundColor: timeRemaining <= 10 ? '#e74c3c' : '#3498db'
              }}
            />
          </div>
        )}
      </div>

      <div className="choices-container">
        <div className="choices-grid">
          {choice.options.map((option, index) => (
            <ChoiceCard
              key={index}
              option={option}
              onSelect={handleOptionSelect}
              disabled={isSubmitting}
              showEffects={showEffects}
            />
          ))}
        </div>
      </div>

      {selectedOption && (
        <div className="choice-feedback">
          <div className="feedback-content">
            <div className="feedback-icon">✨</div>
            <div className="feedback-text">
              You chose: "{selectedOption.text}"
            </div>
            <div className="feedback-story">
              {selectedOption.story}
            </div>
          </div>
        </div>
      )}

      {isSubmitting && (
        <div className="choice-loading">
          <div className="loading-spinner" />
          <div className="loading-text">Processing your choice...</div>
        </div>
      )}
    </div>
  );
};

export default ChoicePresentation;
