/* TimelineVisualization component styles */

.timeline-visualization {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.timeline-visualization.animated {
  animation: timelineSlideIn 0.8s ease-out;
}

@keyframes timelineSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.timeline-header {
  margin-bottom: 20px;
  text-align: center;
}

.timeline-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3em;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.timeline-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 0;
  margin-bottom: 20px;
}

.timeline-line {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #e0e0e0 0%, #bdbdbd 50%, #e0e0e0 100%);
  border-radius: 2px;
  z-index: 1;
}

.timeline-stage {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
  transition: all 0.3s ease;
  cursor: pointer;
}

.timeline-stage:hover {
  transform: translateY(-5px);
}

.stage-marker {
  position: relative;
  margin-bottom: 12px;
}

.stage-dot {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5em;
  background: var(--stage-color, #e0e0e0);
  border: 3px solid #fff;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stage-dot::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  border-radius: 50%;
}

.stage-emoji {
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.current-pulse {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px solid var(--stage-color, #667eea);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.stage-label {
  text-align: center;
  min-width: 80px;
}

.stage-title {
  font-size: 0.85em;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  line-height: 1.2;
}

.stage-age {
  font-size: 0.75em;
  color: #666;
  font-weight: 500;
}

/* Stage states */
.timeline-stage.completed .stage-dot {
  background: #27ae60;
  transform: scale(1.1);
}

.timeline-stage.current .stage-dot {
  background: var(--stage-color);
  transform: scale(1.2);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.timeline-stage.current .stage-title {
  color: #667eea;
  font-weight: 700;
}

.timeline-stage.past .stage-dot {
  background: #95a5a6;
  opacity: 0.8;
}

.timeline-stage.future .stage-dot {
  background: #ecf0f1;
  color: #bdc3c7;
  opacity: 0.6;
}

.timeline-stage.future .stage-title {
  color: #95a5a6;
}

.timeline-stage.future .stage-age {
  color: #bdc3c7;
}

.timeline-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8em;
  color: #666;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.legend-dot.completed {
  background: #27ae60;
}

.legend-dot.current {
  background: #667eea;
}

.legend-dot.future {
  background: #ecf0f1;
}

/* Responsive design */
@media (max-width: 768px) {
  .timeline-visualization {
    padding: 20px;
  }
  
  .timeline-container {
    flex-direction: column;
    gap: 20px;
    padding: 0;
  }
  
  .timeline-line {
    display: none;
  }
  
  .timeline-stage {
    flex-direction: row;
    width: 100%;
    text-align: left;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .stage-marker {
    margin-bottom: 0;
    margin-right: 16px;
  }
  
  .stage-dot {
    width: 40px;
    height: 40px;
    font-size: 1.2em;
  }
  
  .stage-label {
    text-align: left;
    min-width: auto;
    flex: 1;
  }
  
  .timeline-legend {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .timeline-visualization {
    padding: 16px;
  }
  
  .timeline-header h3 {
    font-size: 1.1em;
  }
  
  .timeline-stage {
    padding: 10px;
  }
  
  .stage-dot {
    width: 35px;
    height: 35px;
    font-size: 1.1em;
  }
  
  .stage-title {
    font-size: 0.8em;
  }
  
  .stage-age {
    font-size: 0.7em;
  }
  
  .timeline-legend {
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .legend-item {
    font-size: 0.75em;
  }
}
