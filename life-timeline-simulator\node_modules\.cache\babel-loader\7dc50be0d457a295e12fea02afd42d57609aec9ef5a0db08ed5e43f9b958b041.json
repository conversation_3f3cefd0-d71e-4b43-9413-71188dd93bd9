{"ast": null, "code": "// Utility functions for game mechanics\n\nimport { STAT_LIMITS, LIFE_STAGES, STAGE_CONFIG } from '../data/gameData';\n\n/**\n * Apply stat changes while respecting min/max limits\n * @param {Object} currentStats - Current player stats\n * @param {Object} effects - Stat changes to apply\n * @returns {Object} Updated stats\n */\nexport const applyStatEffects = (currentStats, effects) => {\n  const newStats = {\n    ...currentStats\n  };\n  Object.keys(effects).forEach(stat => {\n    newStats[stat] = Math.max(STAT_LIMITS.MIN, Math.min(STAT_LIMITS.MAX, newStats[stat] + effects[stat]));\n  });\n  return newStats;\n};\n\n/**\n * Get the next life stage\n * @param {string} currentStage - Current life stage\n * @returns {string|null} Next stage or null if at the end\n */\nexport const getNextStage = currentStage => {\n  const stages = Object.values(LIFE_STAGES);\n  const currentIndex = stages.indexOf(currentStage);\n  if (currentIndex === -1 || currentIndex === stages.length - 1) {\n    return null;\n  }\n  return stages[currentIndex + 1];\n};\n\n/**\n * Check if the game is complete\n * @param {string} currentStage - Current life stage\n * @returns {boolean} True if game is complete\n */\nexport const isGameComplete = currentStage => {\n  return currentStage === LIFE_STAGES.OLD_AGE;\n};\n\n/**\n * Calculate overall life score based on final stats\n * @param {Object} stats - Final player stats\n * @returns {number} Overall score (0-100)\n */\nexport const calculateLifeScore = stats => {\n  const totalStats = Object.values(stats).reduce((sum, stat) => sum + stat, 0);\n  return Math.round(totalStats / Object.keys(stats).length);\n};\n\n/**\n * Get life evaluation based on score\n * @param {number} score - Life score (0-100)\n * @returns {Object} Evaluation with title and description\n */\nexport const getLifeEvaluation = score => {\n  if (score >= 90) {\n    return {\n      title: \"Exceptional Life! 🌟\",\n      description: \"You lived an extraordinary life, excelling in all areas and finding true fulfillment.\"\n    };\n  } else if (score >= 80) {\n    return {\n      title: \"Great Life! 🎉\",\n      description: \"You lived a wonderful life with strong relationships, good health, and happiness.\"\n    };\n  } else if (score >= 70) {\n    return {\n      title: \"Good Life 😊\",\n      description: \"You lived a solid life with some great moments and meaningful experiences.\"\n    };\n  } else if (score >= 60) {\n    return {\n      title: \"Average Life 😐\",\n      description: \"You lived a decent life with both ups and downs, learning from your experiences.\"\n    };\n  } else if (score >= 50) {\n    return {\n      title: \"Challenging Life 😔\",\n      description: \"Life had its difficulties, but you persevered through tough times.\"\n    };\n  } else {\n    return {\n      title: \"Difficult Life 😢\",\n      description: \"Life was quite challenging, but every experience taught you something valuable.\"\n    };\n  }\n};\n\n/**\n * Get stat color based on value\n * @param {number} value - Stat value (0-100)\n * @returns {string} CSS color\n */\nexport const getStatColor = value => {\n  if (value >= 80) return '#4CAF50'; // Green\n  if (value >= 60) return '#8BC34A'; // Light Green\n  if (value >= 40) return '#FFC107'; // Yellow\n  if (value >= 20) return '#FF9800'; // Orange\n  return '#F44336'; // Red\n};\n\n/**\n * Get stat icon based on stat type\n * @param {string} statType - Type of stat\n * @returns {string} Emoji icon\n */\nexport const getStatIcon = statType => {\n  const icons = {\n    health: '❤️',\n    happiness: '😊',\n    money: '💰',\n    relationships: '👥'\n  };\n  return icons[statType] || '📊';\n};\n\n/**\n * Format stat name for display\n * @param {string} statType - Type of stat\n * @returns {string} Formatted name\n */\nexport const formatStatName = statType => {\n  return statType.charAt(0).toUpperCase() + statType.slice(1);\n};\n\n/**\n * Generate a random choice if user takes too long (optional feature)\n * @param {Array} choices - Available choices\n * @returns {Object} Random choice\n */\nexport const getRandomChoice = choices => {\n  const randomIndex = Math.floor(Math.random() * choices.length);\n  return choices[randomIndex];\n};\n\n/**\n * Save game state to localStorage\n * @param {Object} gameState - Current game state\n */\nexport const saveGameState = gameState => {\n  try {\n    localStorage.setItem('lts_game_state', JSON.stringify(gameState));\n  } catch (error) {\n    console.error('Failed to save game state:', error);\n  }\n};\n\n/**\n * Load game state from localStorage\n * @returns {Object|null} Saved game state or null\n */\nexport const loadGameState = () => {\n  try {\n    const savedState = localStorage.getItem('lts_game_state');\n    return savedState ? JSON.parse(savedState) : null;\n  } catch (error) {\n    console.error('Failed to load game state:', error);\n    return null;\n  }\n};\n\n/**\n * Clear saved game state\n */\nexport const clearGameState = () => {\n  try {\n    localStorage.removeItem('lts_game_state');\n  } catch (error) {\n    console.error('Failed to clear game state:', error);\n  }\n};\n\n/**\n * Get progress percentage through life stages\n * @param {string} currentStage - Current life stage\n * @returns {number} Progress percentage (0-100)\n */\nexport const getLifeProgress = currentStage => {\n  const stages = Object.values(LIFE_STAGES);\n  const currentIndex = stages.indexOf(currentStage);\n  if (currentIndex === -1) return 0;\n  return Math.round((currentIndex + 1) / stages.length * 100);\n};\n\n/**\n * Get stage configuration\n * @param {string} stage - Life stage\n * @returns {Object} Stage configuration\n */\nexport const getStageConfig = stage => {\n  return STAGE_CONFIG[stage] || {};\n};", "map": {"version": 3, "names": ["STAT_LIMITS", "LIFE_STAGES", "STAGE_CONFIG", "applyStatEffects", "currentStats", "effects", "newStats", "Object", "keys", "for<PERSON>ach", "stat", "Math", "max", "MIN", "min", "MAX", "getNextStage", "currentStage", "stages", "values", "currentIndex", "indexOf", "length", "isGameComplete", "OLD_AGE", "calculateLifeScore", "stats", "totalStats", "reduce", "sum", "round", "getLifeEvaluation", "score", "title", "description", "getStatColor", "value", "getStatIcon", "statType", "icons", "health", "happiness", "money", "relationships", "formatStatName", "char<PERSON>t", "toUpperCase", "slice", "getRandomChoice", "choices", "randomIndex", "floor", "random", "saveGameState", "gameState", "localStorage", "setItem", "JSON", "stringify", "error", "console", "loadGameState", "savedState", "getItem", "parse", "clearGameState", "removeItem", "getLifeProgress", "getStageConfig", "stage"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/utils/gameUtils.js"], "sourcesContent": ["// Utility functions for game mechanics\n\nimport { STAT_LIMITS, LIFE_STAGES, STAGE_CONFIG } from '../data/gameData';\n\n/**\n * Apply stat changes while respecting min/max limits\n * @param {Object} currentStats - Current player stats\n * @param {Object} effects - Stat changes to apply\n * @returns {Object} Updated stats\n */\nexport const applyStatEffects = (currentStats, effects) => {\n  const newStats = { ...currentStats };\n  \n  Object.keys(effects).forEach(stat => {\n    newStats[stat] = Math.max(\n      STAT_LIMITS.MIN,\n      Math.min(STAT_LIMITS.MAX, newStats[stat] + effects[stat])\n    );\n  });\n  \n  return newStats;\n};\n\n/**\n * Get the next life stage\n * @param {string} currentStage - Current life stage\n * @returns {string|null} Next stage or null if at the end\n */\nexport const getNextStage = (currentStage) => {\n  const stages = Object.values(LIFE_STAGES);\n  const currentIndex = stages.indexOf(currentStage);\n  \n  if (currentIndex === -1 || currentIndex === stages.length - 1) {\n    return null;\n  }\n  \n  return stages[currentIndex + 1];\n};\n\n/**\n * Check if the game is complete\n * @param {string} currentStage - Current life stage\n * @returns {boolean} True if game is complete\n */\nexport const isGameComplete = (currentStage) => {\n  return currentStage === LIFE_STAGES.OLD_AGE;\n};\n\n/**\n * Calculate overall life score based on final stats\n * @param {Object} stats - Final player stats\n * @returns {number} Overall score (0-100)\n */\nexport const calculateLifeScore = (stats) => {\n  const totalStats = Object.values(stats).reduce((sum, stat) => sum + stat, 0);\n  return Math.round(totalStats / Object.keys(stats).length);\n};\n\n/**\n * Get life evaluation based on score\n * @param {number} score - Life score (0-100)\n * @returns {Object} Evaluation with title and description\n */\nexport const getLifeEvaluation = (score) => {\n  if (score >= 90) {\n    return {\n      title: \"Exceptional Life! 🌟\",\n      description: \"You lived an extraordinary life, excelling in all areas and finding true fulfillment.\"\n    };\n  } else if (score >= 80) {\n    return {\n      title: \"Great Life! 🎉\",\n      description: \"You lived a wonderful life with strong relationships, good health, and happiness.\"\n    };\n  } else if (score >= 70) {\n    return {\n      title: \"Good Life 😊\",\n      description: \"You lived a solid life with some great moments and meaningful experiences.\"\n    };\n  } else if (score >= 60) {\n    return {\n      title: \"Average Life 😐\",\n      description: \"You lived a decent life with both ups and downs, learning from your experiences.\"\n    };\n  } else if (score >= 50) {\n    return {\n      title: \"Challenging Life 😔\",\n      description: \"Life had its difficulties, but you persevered through tough times.\"\n    };\n  } else {\n    return {\n      title: \"Difficult Life 😢\",\n      description: \"Life was quite challenging, but every experience taught you something valuable.\"\n    };\n  }\n};\n\n/**\n * Get stat color based on value\n * @param {number} value - Stat value (0-100)\n * @returns {string} CSS color\n */\nexport const getStatColor = (value) => {\n  if (value >= 80) return '#4CAF50'; // Green\n  if (value >= 60) return '#8BC34A'; // Light Green\n  if (value >= 40) return '#FFC107'; // Yellow\n  if (value >= 20) return '#FF9800'; // Orange\n  return '#F44336'; // Red\n};\n\n/**\n * Get stat icon based on stat type\n * @param {string} statType - Type of stat\n * @returns {string} Emoji icon\n */\nexport const getStatIcon = (statType) => {\n  const icons = {\n    health: '❤️',\n    happiness: '😊',\n    money: '💰',\n    relationships: '👥'\n  };\n  return icons[statType] || '📊';\n};\n\n/**\n * Format stat name for display\n * @param {string} statType - Type of stat\n * @returns {string} Formatted name\n */\nexport const formatStatName = (statType) => {\n  return statType.charAt(0).toUpperCase() + statType.slice(1);\n};\n\n/**\n * Generate a random choice if user takes too long (optional feature)\n * @param {Array} choices - Available choices\n * @returns {Object} Random choice\n */\nexport const getRandomChoice = (choices) => {\n  const randomIndex = Math.floor(Math.random() * choices.length);\n  return choices[randomIndex];\n};\n\n/**\n * Save game state to localStorage\n * @param {Object} gameState - Current game state\n */\nexport const saveGameState = (gameState) => {\n  try {\n    localStorage.setItem('lts_game_state', JSON.stringify(gameState));\n  } catch (error) {\n    console.error('Failed to save game state:', error);\n  }\n};\n\n/**\n * Load game state from localStorage\n * @returns {Object|null} Saved game state or null\n */\nexport const loadGameState = () => {\n  try {\n    const savedState = localStorage.getItem('lts_game_state');\n    return savedState ? JSON.parse(savedState) : null;\n  } catch (error) {\n    console.error('Failed to load game state:', error);\n    return null;\n  }\n};\n\n/**\n * Clear saved game state\n */\nexport const clearGameState = () => {\n  try {\n    localStorage.removeItem('lts_game_state');\n  } catch (error) {\n    console.error('Failed to clear game state:', error);\n  }\n};\n\n/**\n * Get progress percentage through life stages\n * @param {string} currentStage - Current life stage\n * @returns {number} Progress percentage (0-100)\n */\nexport const getLifeProgress = (currentStage) => {\n  const stages = Object.values(LIFE_STAGES);\n  const currentIndex = stages.indexOf(currentStage);\n  \n  if (currentIndex === -1) return 0;\n  \n  return Math.round(((currentIndex + 1) / stages.length) * 100);\n};\n\n/**\n * Get stage configuration\n * @param {string} stage - Life stage\n * @returns {Object} Stage configuration\n */\nexport const getStageConfig = (stage) => {\n  return STAGE_CONFIG[stage] || {};\n};\n"], "mappings": "AAAA;;AAEA,SAASA,WAAW,EAAEC,WAAW,EAAEC,YAAY,QAAQ,kBAAkB;;AAEzE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,YAAY,EAAEC,OAAO,KAAK;EACzD,MAAMC,QAAQ,GAAG;IAAE,GAAGF;EAAa,CAAC;EAEpCG,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,IAAI,IAAI;IACnCJ,QAAQ,CAACI,IAAI,CAAC,GAAGC,IAAI,CAACC,GAAG,CACvBZ,WAAW,CAACa,GAAG,EACfF,IAAI,CAACG,GAAG,CAACd,WAAW,CAACe,GAAG,EAAET,QAAQ,CAACI,IAAI,CAAC,GAAGL,OAAO,CAACK,IAAI,CAAC,CAC1D,CAAC;EACH,CAAC,CAAC;EAEF,OAAOJ,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,YAAY,GAAIC,YAAY,IAAK;EAC5C,MAAMC,MAAM,GAAGX,MAAM,CAACY,MAAM,CAAClB,WAAW,CAAC;EACzC,MAAMmB,YAAY,GAAGF,MAAM,CAACG,OAAO,CAACJ,YAAY,CAAC;EAEjD,IAAIG,YAAY,KAAK,CAAC,CAAC,IAAIA,YAAY,KAAKF,MAAM,CAACI,MAAM,GAAG,CAAC,EAAE;IAC7D,OAAO,IAAI;EACb;EAEA,OAAOJ,MAAM,CAACE,YAAY,GAAG,CAAC,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,cAAc,GAAIN,YAAY,IAAK;EAC9C,OAAOA,YAAY,KAAKhB,WAAW,CAACuB,OAAO;AAC7C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;EAC3C,MAAMC,UAAU,GAAGpB,MAAM,CAACY,MAAM,CAACO,KAAK,CAAC,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEnB,IAAI,KAAKmB,GAAG,GAAGnB,IAAI,EAAE,CAAC,CAAC;EAC5E,OAAOC,IAAI,CAACmB,KAAK,CAACH,UAAU,GAAGpB,MAAM,CAACC,IAAI,CAACkB,KAAK,CAAC,CAACJ,MAAM,CAAC;AAC3D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,iBAAiB,GAAIC,KAAK,IAAK;EAC1C,IAAIA,KAAK,IAAI,EAAE,EAAE;IACf,OAAO;MACLC,KAAK,EAAE,sBAAsB;MAC7BC,WAAW,EAAE;IACf,CAAC;EACH,CAAC,MAAM,IAAIF,KAAK,IAAI,EAAE,EAAE;IACtB,OAAO;MACLC,KAAK,EAAE,gBAAgB;MACvBC,WAAW,EAAE;IACf,CAAC;EACH,CAAC,MAAM,IAAIF,KAAK,IAAI,EAAE,EAAE;IACtB,OAAO;MACLC,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE;IACf,CAAC;EACH,CAAC,MAAM,IAAIF,KAAK,IAAI,EAAE,EAAE;IACtB,OAAO;MACLC,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE;IACf,CAAC;EACH,CAAC,MAAM,IAAIF,KAAK,IAAI,EAAE,EAAE;IACtB,OAAO;MACLC,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE;IACf,CAAC;EACH,CAAC,MAAM;IACL,OAAO;MACLD,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAIC,KAAK,IAAK;EACrC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;EACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;EACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;EACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;EACnC,OAAO,SAAS,CAAC,CAAC;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,WAAW,GAAIC,QAAQ,IAAK;EACvC,MAAMC,KAAK,GAAG;IACZC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,IAAI;IACXC,aAAa,EAAE;EACjB,CAAC;EACD,OAAOJ,KAAK,CAACD,QAAQ,CAAC,IAAI,IAAI;AAChC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,cAAc,GAAIN,QAAQ,IAAK;EAC1C,OAAOA,QAAQ,CAACO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGR,QAAQ,CAACS,KAAK,CAAC,CAAC,CAAC;AAC7D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAIC,OAAO,IAAK;EAC1C,MAAMC,WAAW,GAAGvC,IAAI,CAACwC,KAAK,CAACxC,IAAI,CAACyC,MAAM,CAAC,CAAC,GAAGH,OAAO,CAAC3B,MAAM,CAAC;EAC9D,OAAO2B,OAAO,CAACC,WAAW,CAAC;AAC7B,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMG,aAAa,GAAIC,SAAS,IAAK;EAC1C,IAAI;IACFC,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEC,IAAI,CAACC,SAAS,CAACJ,SAAS,CAAC,CAAC;EACnE,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;EACpD;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAME,aAAa,GAAGA,CAAA,KAAM;EACjC,IAAI;IACF,MAAMC,UAAU,GAAGP,YAAY,CAACQ,OAAO,CAAC,gBAAgB,CAAC;IACzD,OAAOD,UAAU,GAAGL,IAAI,CAACO,KAAK,CAACF,UAAU,CAAC,GAAG,IAAI;EACnD,CAAC,CAAC,OAAOH,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMM,cAAc,GAAGA,CAAA,KAAM;EAClC,IAAI;IACFV,YAAY,CAACW,UAAU,CAAC,gBAAgB,CAAC;EAC3C,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;EACrD;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,eAAe,GAAIlD,YAAY,IAAK;EAC/C,MAAMC,MAAM,GAAGX,MAAM,CAACY,MAAM,CAAClB,WAAW,CAAC;EACzC,MAAMmB,YAAY,GAAGF,MAAM,CAACG,OAAO,CAACJ,YAAY,CAAC;EAEjD,IAAIG,YAAY,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;EAEjC,OAAOT,IAAI,CAACmB,KAAK,CAAE,CAACV,YAAY,GAAG,CAAC,IAAIF,MAAM,CAACI,MAAM,GAAI,GAAG,CAAC;AAC/D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM8C,cAAc,GAAIC,KAAK,IAAK;EACvC,OAAOnE,YAAY,CAACmE,KAAK,CAAC,IAAI,CAAC,CAAC;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}