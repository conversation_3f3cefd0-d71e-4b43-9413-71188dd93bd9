{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LTS\\\\life-timeline-simulator\\\\src\\\\components\\\\ChoicePresentation.js\",\n  _s = $RefreshSig$();\n// Main choice presentation component\n\nimport React, { useState, useEffect } from 'react';\nimport ChoiceCard from './ChoiceCard';\nimport '../styles/ChoicePresentation.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChoicePresentation = ({\n  choice,\n  onChoiceSelect,\n  showEffects = true,\n  timeLimit = null\n}) => {\n  _s();\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [timeRemaining, setTimeRemaining] = useState(timeLimit);\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLimit && timeRemaining > 0) {\n      const timer = setTimeout(() => {\n        setTimeRemaining(timeRemaining - 1);\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLimit && timeRemaining === 0) {\n      // Auto-select random option when time runs out\n      const randomOption = choice.options[Math.floor(Math.random() * choice.options.length)];\n      handleOptionSelect(randomOption);\n    }\n  }, [timeRemaining, timeLimit, choice.options]);\n  const handleOptionSelect = async option => {\n    if (isSubmitting) return;\n    setSelectedOption(option);\n    setIsSubmitting(true);\n\n    // Add a small delay for visual feedback\n    setTimeout(() => {\n      onChoiceSelect(choice, option);\n      setIsSubmitting(false);\n    }, 800);\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"choice-presentation\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"choice-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"choice-title\",\n        children: choice.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"choice-description\",\n        children: choice.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), timeLimit && timeRemaining > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"choice-timer\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timer-icon\",\n          children: \"\\u23F0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timer-text\",\n          children: [\"Time remaining: \", formatTime(timeRemaining)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timer-bar\",\n          style: {\n            width: `${timeRemaining / timeLimit * 100}%`,\n            backgroundColor: timeRemaining <= 10 ? '#e74c3c' : '#3498db'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"choices-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"choices-grid\",\n        children: choice.options.map((option, index) => /*#__PURE__*/_jsxDEV(ChoiceCard, {\n          option: option,\n          onSelect: handleOptionSelect,\n          disabled: isSubmitting,\n          showEffects: showEffects\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), selectedOption && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"choice-feedback\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feedback-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feedback-icon\",\n          children: \"\\u2728\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feedback-text\",\n          children: [\"You chose: \\\"\", selectedOption.text, \"\\\"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feedback-story\",\n          children: selectedOption.story\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 9\n    }, this), isSubmitting && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"choice-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-text\",\n        children: \"Processing your choice...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(ChoicePresentation, \"L5YgbJNo0/pZTMz8az2eyIcZq+M=\");\n_c = ChoicePresentation;\nexport default ChoicePresentation;\nvar _c;\n$RefreshReg$(_c, \"ChoicePresentation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ChoiceCard", "jsxDEV", "_jsxDEV", "ChoicePresentation", "choice", "onChoiceSelect", "showEffects", "timeLimit", "_s", "selectedOption", "setSelectedOption", "isSubmitting", "setIsSubmitting", "timeRemaining", "setTimeRemaining", "timer", "setTimeout", "clearTimeout", "randomOption", "options", "Math", "floor", "random", "length", "handleOptionSelect", "option", "formatTime", "seconds", "mins", "secs", "toString", "padStart", "className", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "style", "width", "backgroundColor", "map", "index", "onSelect", "disabled", "text", "story", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/components/ChoicePresentation.js"], "sourcesContent": ["// Main choice presentation component\n\nimport React, { useState, useEffect } from 'react';\nimport ChoiceCard from './ChoiceCard';\nimport '../styles/ChoicePresentation.css';\n\nconst ChoicePresentation = ({ \n  choice, \n  onChoiceSelect,\n  showEffects = true,\n  timeLimit = null \n}) => {\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [timeRemaining, setTimeRemaining] = useState(timeLimit);\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLimit && timeRemaining > 0) {\n      const timer = setTimeout(() => {\n        setTimeRemaining(timeRemaining - 1);\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else if (timeLimit && timeRemaining === 0) {\n      // Auto-select random option when time runs out\n      const randomOption = choice.options[Math.floor(Math.random() * choice.options.length)];\n      handleOptionSelect(randomOption);\n    }\n  }, [timeRemaining, timeLimit, choice.options]);\n\n  const handleOptionSelect = async (option) => {\n    if (isSubmitting) return;\n    \n    setSelectedOption(option);\n    setIsSubmitting(true);\n    \n    // Add a small delay for visual feedback\n    setTimeout(() => {\n      onChoiceSelect(choice, option);\n      setIsSubmitting(false);\n    }, 800);\n  };\n\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  return (\n    <div className=\"choice-presentation\">\n      <div className=\"choice-header\">\n        <h2 className=\"choice-title\">{choice.title}</h2>\n        <p className=\"choice-description\">{choice.description}</p>\n        \n        {timeLimit && timeRemaining > 0 && (\n          <div className=\"choice-timer\">\n            <div className=\"timer-icon\">⏰</div>\n            <div className=\"timer-text\">\n              Time remaining: {formatTime(timeRemaining)}\n            </div>\n            <div \n              className=\"timer-bar\"\n              style={{ \n                width: `${(timeRemaining / timeLimit) * 100}%`,\n                backgroundColor: timeRemaining <= 10 ? '#e74c3c' : '#3498db'\n              }}\n            />\n          </div>\n        )}\n      </div>\n\n      <div className=\"choices-container\">\n        <div className=\"choices-grid\">\n          {choice.options.map((option, index) => (\n            <ChoiceCard\n              key={index}\n              option={option}\n              onSelect={handleOptionSelect}\n              disabled={isSubmitting}\n              showEffects={showEffects}\n            />\n          ))}\n        </div>\n      </div>\n\n      {selectedOption && (\n        <div className=\"choice-feedback\">\n          <div className=\"feedback-content\">\n            <div className=\"feedback-icon\">✨</div>\n            <div className=\"feedback-text\">\n              You chose: \"{selectedOption.text}\"\n            </div>\n            <div className=\"feedback-story\">\n              {selectedOption.story}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {isSubmitting && (\n        <div className=\"choice-loading\">\n          <div className=\"loading-spinner\" />\n          <div className=\"loading-text\">Processing your choice...</div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ChoicePresentation;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAO,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,MAAM;EACNC,cAAc;EACdC,WAAW,GAAG,IAAI;EAClBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAACS,SAAS,CAAC;;EAE7D;EACAR,SAAS,CAAC,MAAM;IACd,IAAIQ,SAAS,IAAIM,aAAa,GAAG,CAAC,EAAE;MAClC,MAAME,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BF,gBAAgB,CAACD,aAAa,GAAG,CAAC,CAAC;MACrC,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMI,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIR,SAAS,IAAIM,aAAa,KAAK,CAAC,EAAE;MAC3C;MACA,MAAMK,YAAY,GAAGd,MAAM,CAACe,OAAO,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGlB,MAAM,CAACe,OAAO,CAACI,MAAM,CAAC,CAAC;MACtFC,kBAAkB,CAACN,YAAY,CAAC;IAClC;EACF,CAAC,EAAE,CAACL,aAAa,EAAEN,SAAS,EAAEH,MAAM,CAACe,OAAO,CAAC,CAAC;EAE9C,MAAMK,kBAAkB,GAAG,MAAOC,MAAM,IAAK;IAC3C,IAAId,YAAY,EAAE;IAElBD,iBAAiB,CAACe,MAAM,CAAC;IACzBb,eAAe,CAAC,IAAI,CAAC;;IAErB;IACAI,UAAU,CAAC,MAAM;MACfX,cAAc,CAACD,MAAM,EAAEqB,MAAM,CAAC;MAC9Bb,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMc,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGR,IAAI,CAACC,KAAK,CAACM,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIC,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,oBACE7B,OAAA;IAAK8B,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClC/B,OAAA;MAAK8B,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B/B,OAAA;QAAI8B,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAE7B,MAAM,CAAC8B;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChDpC,OAAA;QAAG8B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAE7B,MAAM,CAACmC;MAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEzD/B,SAAS,IAAIM,aAAa,GAAG,CAAC,iBAC7BX,OAAA;QAAK8B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B/B,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCpC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,kBACV,EAACP,UAAU,CAACb,aAAa,CAAC;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNpC,OAAA;UACE8B,SAAS,EAAC,WAAW;UACrBQ,KAAK,EAAE;YACLC,KAAK,EAAE,GAAI5B,aAAa,GAAGN,SAAS,GAAI,GAAG,GAAG;YAC9CmC,eAAe,EAAE7B,aAAa,IAAI,EAAE,GAAG,SAAS,GAAG;UACrD;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENpC,OAAA;MAAK8B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC/B,OAAA;QAAK8B,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1B7B,MAAM,CAACe,OAAO,CAACwB,GAAG,CAAC,CAAClB,MAAM,EAAEmB,KAAK,kBAChC1C,OAAA,CAACF,UAAU;UAETyB,MAAM,EAAEA,MAAO;UACfoB,QAAQ,EAAErB,kBAAmB;UAC7BsB,QAAQ,EAAEnC,YAAa;UACvBL,WAAW,EAAEA;QAAY,GAJpBsC,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL7B,cAAc,iBACbP,OAAA;MAAK8B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9B/B,OAAA;QAAK8B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/B,OAAA;UAAK8B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtCpC,OAAA;UAAK8B,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,eACjB,EAACxB,cAAc,CAACsC,IAAI,EAAC,IACnC;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpC,OAAA;UAAK8B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BxB,cAAc,CAACuC;QAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA3B,YAAY,iBACXT,OAAA;MAAK8B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B/B,OAAA;QAAK8B,SAAS,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnCpC,OAAA;QAAK8B,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAtGIL,kBAAkB;AAAA8C,EAAA,GAAlB9C,kBAAkB;AAwGxB,eAAeA,kBAAkB;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}