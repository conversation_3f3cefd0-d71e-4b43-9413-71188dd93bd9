{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LTS\\\\life-timeline-simulator\\\\src\\\\components\\\\ChoiceCard.js\",\n  _s = $RefreshSig$();\n// Individual choice card component\n\nimport React, { useState } from 'react';\nimport { getStatIcon, formatStatName } from '../utils/gameUtils';\nimport '../styles/ChoiceCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChoiceCard = ({\n  option,\n  onSelect,\n  disabled = false,\n  showEffects = false\n}) => {\n  _s();\n  const [isHovered, setIsHovered] = useState(false);\n  const handleClick = () => {\n    if (!disabled) {\n      onSelect(option);\n    }\n  };\n  const getEffectColor = value => {\n    if (value > 0) return '#27ae60';\n    if (value < 0) return '#e74c3c';\n    return '#95a5a6';\n  };\n  const getEffectIcon = value => {\n    if (value > 0) return '↗️';\n    if (value < 0) return '↘️';\n    return '➡️';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `choice-card ${disabled ? 'disabled' : ''} ${isHovered ? 'hovered' : ''}`,\n    onClick: handleClick,\n    onMouseEnter: () => setIsHovered(true),\n    onMouseLeave: () => setIsHovered(false),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"choice-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"choice-text\",\n        children: option.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), showEffects && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"choice-effects\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"effects-header\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"effects-label\",\n            children: \"Effects:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"effects-grid\",\n          children: Object.entries(option.effects).map(([stat, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"effect-item\",\n            style: {\n              color: getEffectColor(value)\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"effect-icon\",\n              children: getStatIcon(stat)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"effect-name\",\n              children: formatStatName(stat)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"effect-value\",\n              children: [getEffectIcon(value), \" \", value > 0 ? '+' : '', value]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 19\n            }, this)]\n          }, stat, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"choice-hover-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hover-text\",\n        children: \"Choose this path\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"choice-ripple\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(ChoiceCard, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n_c = ChoiceCard;\nexport default ChoiceCard;\nvar _c;\n$RefreshReg$(_c, \"ChoiceCard\");", "map": {"version": 3, "names": ["React", "useState", "getStatIcon", "formatStatName", "jsxDEV", "_jsxDEV", "ChoiceCard", "option", "onSelect", "disabled", "showEffects", "_s", "isHovered", "setIsHovered", "handleClick", "getEffectColor", "value", "getEffectIcon", "className", "onClick", "onMouseEnter", "onMouseLeave", "children", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Object", "entries", "effects", "map", "stat", "style", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/components/ChoiceCard.js"], "sourcesContent": ["// Individual choice card component\n\nimport React, { useState } from 'react';\nimport { getStatIcon, formatStatName } from '../utils/gameUtils';\nimport '../styles/ChoiceCard.css';\n\nconst ChoiceCard = ({ \n  option, \n  onSelect, \n  disabled = false,\n  showEffects = false \n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  const handleClick = () => {\n    if (!disabled) {\n      onSelect(option);\n    }\n  };\n\n  const getEffectColor = (value) => {\n    if (value > 0) return '#27ae60';\n    if (value < 0) return '#e74c3c';\n    return '#95a5a6';\n  };\n\n  const getEffectIcon = (value) => {\n    if (value > 0) return '↗️';\n    if (value < 0) return '↘️';\n    return '➡️';\n  };\n\n  return (\n    <div \n      className={`choice-card ${disabled ? 'disabled' : ''} ${isHovered ? 'hovered' : ''}`}\n      onClick={handleClick}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      <div className=\"choice-content\">\n        <div className=\"choice-text\">\n          {option.text}\n        </div>\n        \n        {showEffects && (\n          <div className=\"choice-effects\">\n            <div className=\"effects-header\">\n              <span className=\"effects-label\">Effects:</span>\n            </div>\n            <div className=\"effects-grid\">\n              {Object.entries(option.effects).map(([stat, value]) => (\n                <div \n                  key={stat}\n                  className=\"effect-item\"\n                  style={{ color: getEffectColor(value) }}\n                >\n                  <span className=\"effect-icon\">{getStatIcon(stat)}</span>\n                  <span className=\"effect-name\">{formatStatName(stat)}</span>\n                  <span className=\"effect-value\">\n                    {getEffectIcon(value)} {value > 0 ? '+' : ''}{value}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n      \n      <div className=\"choice-hover-overlay\">\n        <div className=\"hover-text\">Choose this path</div>\n      </div>\n      \n      <div className=\"choice-ripple\" />\n    </div>\n  );\n};\n\nexport default ChoiceCard;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,cAAc,QAAQ,oBAAoB;AAChE,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,UAAU,GAAGA,CAAC;EAClBC,MAAM;EACNC,QAAQ;EACRC,QAAQ,GAAG,KAAK;EAChBC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACL,QAAQ,EAAE;MACbD,QAAQ,CAACD,MAAM,CAAC;IAClB;EACF,CAAC;EAED,MAAMQ,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,GAAG,CAAC,EAAE,OAAO,SAAS;IAC/B,IAAIA,KAAK,GAAG,CAAC,EAAE,OAAO,SAAS;IAC/B,OAAO,SAAS;EAClB,CAAC;EAED,MAAMC,aAAa,GAAID,KAAK,IAAK;IAC/B,IAAIA,KAAK,GAAG,CAAC,EAAE,OAAO,IAAI;IAC1B,IAAIA,KAAK,GAAG,CAAC,EAAE,OAAO,IAAI;IAC1B,OAAO,IAAI;EACb,CAAC;EAED,oBACEX,OAAA;IACEa,SAAS,EAAE,eAAeT,QAAQ,GAAG,UAAU,GAAG,EAAE,IAAIG,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;IACrFO,OAAO,EAAEL,WAAY;IACrBM,YAAY,EAAEA,CAAA,KAAMP,YAAY,CAAC,IAAI,CAAE;IACvCQ,YAAY,EAAEA,CAAA,KAAMR,YAAY,CAAC,KAAK,CAAE;IAAAS,QAAA,gBAExCjB,OAAA;MAAKa,SAAS,EAAC,gBAAgB;MAAAI,QAAA,gBAC7BjB,OAAA;QAAKa,SAAS,EAAC,aAAa;QAAAI,QAAA,EACzBf,MAAM,CAACgB;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,EAELjB,WAAW,iBACVL,OAAA;QAAKa,SAAS,EAAC,gBAAgB;QAAAI,QAAA,gBAC7BjB,OAAA;UAAKa,SAAS,EAAC,gBAAgB;UAAAI,QAAA,eAC7BjB,OAAA;YAAMa,SAAS,EAAC,eAAe;YAAAI,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNtB,OAAA;UAAKa,SAAS,EAAC,cAAc;UAAAI,QAAA,EAC1BM,MAAM,CAACC,OAAO,CAACtB,MAAM,CAACuB,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEhB,KAAK,CAAC,kBAChDX,OAAA;YAEEa,SAAS,EAAC,aAAa;YACvBe,KAAK,EAAE;cAAEC,KAAK,EAAEnB,cAAc,CAACC,KAAK;YAAE,CAAE;YAAAM,QAAA,gBAExCjB,OAAA;cAAMa,SAAS,EAAC,aAAa;cAAAI,QAAA,EAAEpB,WAAW,CAAC8B,IAAI;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxDtB,OAAA;cAAMa,SAAS,EAAC,aAAa;cAAAI,QAAA,EAAEnB,cAAc,CAAC6B,IAAI;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3DtB,OAAA;cAAMa,SAAS,EAAC,cAAc;cAAAI,QAAA,GAC3BL,aAAa,CAACD,KAAK,CAAC,EAAC,GAAC,EAACA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,KAAK;YAAA;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA,GARFK,IAAI;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASN,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENtB,OAAA;MAAKa,SAAS,EAAC,sBAAsB;MAAAI,QAAA,eACnCjB,OAAA;QAAKa,SAAS,EAAC,YAAY;QAAAI,QAAA,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eAENtB,OAAA;MAAKa,SAAS,EAAC;IAAe;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9B,CAAC;AAEV,CAAC;AAAChB,EAAA,CArEIL,UAAU;AAAA6B,EAAA,GAAV7B,UAAU;AAuEhB,eAAeA,UAAU;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}