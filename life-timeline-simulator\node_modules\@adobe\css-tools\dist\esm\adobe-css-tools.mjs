class t extends Error{reason;filename;line;column;source;constructor(t,e,i,s,n){super(`${t}:${i}:${s}: ${e}`),this.reason=e,this.filename=t,this.line=i,this.column=s,this.source=n}}class e{start;end;source;constructor(t,e,i){this.start=t,this.end=e,this.source=i}}var i;!function(t){t.stylesheet="stylesheet",t.rule="rule",t.declaration="declaration",t.comment="comment",t.container="container",t.charset="charset",t.document="document",t.customMedia="custom-media",t.fontFace="font-face",t.host="host",t.import="import",t.keyframes="keyframes",t.keyframe="keyframe",t.layer="layer",t.media="media",t.namespace="namespace",t.page="page",t.startingStyle="starting-style",t.supports="supports"}(i||(i={}));const s=(t,e,i)=>{let s=i,n=1e4;do{const i=e.map(e=>t.indexOf(e,s));i.push(t.indexOf("\\",s));const r=i.filter(t=>-1!==t);if(0===r.length)return-1;const o=Math.min(...r);if("\\"!==t[o])return o;s=o+2,n--}while(n>0);throw new Error("Too many escaping")},n=(t,e,i)=>{let r=i,o=1e4;do{const i=e.map(e=>t.indexOf(e,r));i.push(t.indexOf("(",r)),i.push(t.indexOf('"',r)),i.push(t.indexOf("'",r)),i.push(t.indexOf("\\",r));const c=i.filter(t=>-1!==t);if(0===c.length)return-1;const a=Math.min(...c);switch(t[a]){case"\\":r=a+2;break;case"(":{const e=n(t,[")"],a+1);if(-1===e)return-1;r=e+1}break;case'"':{const e=s(t,['"'],a+1);if(-1===e)return-1;r=e+1}break;case"'":{const e=s(t,["'"],a+1);if(-1===e)return-1;r=e+1}break;default:return a}o--}while(o>0);throw new Error("Too many escaping")},r=/\/\*[^]*?(?:\*\/|$)/g;function o(t){return t?t.trim():""}function c(t,e){const i=t&&"string"==typeof t.type,s=i?t:e;for(const e in t){const i=t[e];Array.isArray(i)?i.forEach(t=>{c(t,s)}):i&&"object"==typeof i&&c(i,s)}return i&&Object.defineProperty(t,"parent",{configurable:!0,writable:!0,enumerable:!1,value:e||null}),t}class a{level=0;indentation="  ";compress=!1;constructor(t){"string"==typeof t?.indent&&(this.indentation=t?.indent),t?.compress&&(this.compress=!0)}emit(t,e){return t}indent(t){return this.level=this.level||1,t?(this.level+=t,""):Array(this.level).join(this.indentation)}visit(t){switch(t.type){case i.stylesheet:return this.stylesheet(t);case i.rule:return this.rule(t);case i.declaration:return this.declaration(t);case i.comment:return this.comment(t);case i.container:return this.container(t);case i.charset:return this.charset(t);case i.document:return this.document(t);case i.customMedia:return this.customMedia(t);case i.fontFace:return this.fontFace(t);case i.host:return this.host(t);case i.import:return this.import(t);case i.keyframes:return this.keyframes(t);case i.keyframe:return this.keyframe(t);case i.layer:return this.layer(t);case i.media:return this.media(t);case i.namespace:return this.namespace(t);case i.page:return this.page(t);case i.startingStyle:return this.startingStyle(t);case i.supports:return this.supports(t)}}mapVisit(t,e){let i="";e=e||"";for(let s=0,n=t.length;s<n;s++)i+=this.visit(t[s]),e&&s<n-1&&(i+=this.emit(e));return i}compile(t){return this.compress?t.stylesheet.rules.map(this.visit,this).join(""):this.stylesheet(t)}stylesheet(t){return this.mapVisit(t.stylesheet.rules,"\n\n")}comment(t){return this.compress?this.emit("",t.position):this.emit(`${this.indent()}/*${t.comment}*/`,t.position)}container(t){return this.compress?this.emit(`@container ${t.container}`,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@container ${t.container}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}layer(t){return this.compress?this.emit(`@layer ${t.layer}`,t.position)+(t.rules?this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):";"):this.emit(`${this.indent()}@layer ${t.layer}`,t.position)+(t.rules?this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`):";")}import(t){return this.emit(`@import ${t.import};`,t.position)}media(t){return this.compress?this.emit(`@media ${t.media}`,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@media ${t.media}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}document(t){const e=`@${t.vendor||""}document ${t.document}`;return this.compress?this.emit(e,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(e,t.position)+this.emit(`  {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`${this.indent(-1)}\n}`)}charset(t){return this.emit(`@charset ${t.charset};`,t.position)}namespace(t){return this.emit(`@namespace ${t.namespace};`,t.position)}startingStyle(t){return this.compress?this.emit("@starting-style",t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@starting-style`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}supports(t){return this.compress?this.emit(`@supports ${t.supports}`,t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit(`${this.indent()}@supports ${t.supports}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`\n${this.indent(-1)}${this.indent()}}`)}keyframes(t){return this.compress?this.emit(`@${t.vendor||""}keyframes ${t.name}`,t.position)+this.emit("{")+this.mapVisit(t.keyframes)+this.emit("}"):this.emit(`@${t.vendor||""}keyframes ${t.name}`,t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.keyframes,"\n")+this.emit(`${this.indent(-1)}}`)}keyframe(t){const e=t.declarations;return this.compress?this.emit(t.values.join(","),t.position)+this.emit("{")+this.mapVisit(e)+this.emit("}"):this.emit(this.indent())+this.emit(t.values.join(", "),t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(e,"\n")+this.emit(`${this.indent(-1)}\n${this.indent()}}\n`)}page(t){if(this.compress){const e=t.selectors.length?t.selectors.join(", "):"";return this.emit(`@page ${e}`,t.position)+this.emit("{")+this.mapVisit(t.declarations)+this.emit("}")}const e=t.selectors.length?`${t.selectors.join(", ")} `:"";return this.emit(`@page ${e}`,t.position)+this.emit("{\n")+this.emit(this.indent(1))+this.mapVisit(t.declarations,"\n")+this.emit(this.indent(-1))+this.emit("\n}")}fontFace(t){return this.compress?this.emit("@font-face",t.position)+this.emit("{")+this.mapVisit(t.declarations)+this.emit("}"):this.emit("@font-face ",t.position)+this.emit("{\n")+this.emit(this.indent(1))+this.mapVisit(t.declarations,"\n")+this.emit(this.indent(-1))+this.emit("\n}")}host(t){return this.compress?this.emit("@host",t.position)+this.emit("{")+this.mapVisit(t.rules)+this.emit("}"):this.emit("@host",t.position)+this.emit(` {\n${this.indent(1)}`)+this.mapVisit(t.rules,"\n\n")+this.emit(`${this.indent(-1)}\n}`)}customMedia(t){return this.emit(`@custom-media ${t.name} ${t.media};`,t.position)}rule(t){const e=t.declarations;if(!e.length)return"";if(this.compress)return this.emit(t.selectors.join(","),t.position)+this.emit("{")+this.mapVisit(e)+this.emit("}");const i=this.indent();return this.emit(t.selectors.map(t=>i+t).join(",\n"),t.position)+this.emit(" {\n")+this.emit(this.indent(1))+this.mapVisit(e,"\n")+this.emit(this.indent(-1))+this.emit(`\n${this.indent()}}`)}declaration(t){return this.compress?this.emit(`${t.property}:${t.value}`,t.position)+this.emit(";"):"grid-template-areas"===t.property?this.emit(this.indent())+this.emit(t.property+": "+t.value.split("\n").join("\n".padEnd(22)+this.indent()),t.position)+this.emit(";"):this.emit(this.indent())+this.emit(`${t.property}: ${t.value}`,t.position)+this.emit(";")}}const m=(s,a)=>{a=a||{};let m=1,h=1;function u(){const t={line:m,column:h};return i=>(i.position=new e(t,{line:m,column:h},a?.source||""),$(),i)}const p=[];function l(e){const i=new t(a?.source||"",e,m,h,s);if(!a?.silent)throw i;p.push(i)}function f(){const t=/^{\s*/.exec(s);return!!t&&(g(t),!0)}function d(){const t=/^}/.exec(s);return!!t&&(g(t),!0)}function y(){let t;const e=[];for($(),x(e);s.length&&"}"!==s.charAt(0)&&(t=A()||S(),t);)e.push(t),x(e);return e}function g(t){const e=t[0];return function(t){const e=t.match(/\n/g);e&&(m+=e.length);const i=t.lastIndexOf("\n");h=~i?t.length-i:h+t.length}(e),s=s.slice(e.length),t}function $(){const t=/^\s*/.exec(s);t&&g(t)}function x(t){t=t||[];let e=V();for(;e;)t.push(e),e=V();return t}function V(){const t=u();if("/"!==s.charAt(0)||"*"!==s.charAt(1))return;const e=/^\/\*[^]*?\*\//.exec(s);return e?(g(e),t({type:i.comment,comment:e[0].slice(2,-2)})):l("End of comment missing")}function k(){const t=/^([^{]+)/.exec(s);if(!t)return;g(t);return((t,e)=>{const i=[];let s=0;for(;s<t.length;){const r=n(t,e,s);if(-1===r)return i.push(t.substring(s)),i;i.push(t.substring(s,r)),s=r+1}return i})(o(t[0]).replace(r,""),[","]).map(t=>o(t))}function v(){const t=u(),e=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/.exec(s);if(!e)return;g(e);const c=o(e[0]),a=/^:\s*/.exec(s);if(!a)return l("property missing ':'");g(a);let m="";const h=n(s,[";","}"]);if(-1!==h){m=s.substring(0,h);g([m]),m=o(m).replace(r,"")}const p=t({type:i.declaration,property:c.replace(r,""),value:m}),f=/^[;\s]*/.exec(s);return f&&g(f),p}function w(){const t=[];if(!f())return l("missing '{'");x(t);let e=v();for(;e;)t.push(e),x(t),e=v();return d()?t:l("missing '}'")}function b(){const t=[],e=u();let n=/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/.exec(s);for(;n;){const e=g(n);t.push(e[1]);const i=/^,\s*/.exec(s);i&&g(i),n=/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/.exec(s)}if(t.length)return e({type:i.keyframe,values:t,declarations:w()||[]})}const j=M("import"),O=M("charset"),E=M("namespace");function M(t){const e=new RegExp("^@"+t+"\\s*((?::?[^;'\"]|\"(?:\\\\\"|[^\"])*?\"|'(?:\\\\'|[^'])*?')+)(?:;|$)");return()=>{const i=u(),n=e.exec(s);if(!n)return;const r=g(n),o={type:t};return o[t]=r[1].trim(),i(o)}}function A(){if("@"===s[0])return function(){const t=u(),e=/^@([-\w]+)?keyframes\s*/.exec(s);if(!e)return;const n=g(e)[1],r=/^([-\w]+)\s*/.exec(s);if(!r)return l("@keyframes missing name");const o=g(r)[1];if(!f())return l("@keyframes missing '{'");let c=x(),a=b();for(;a;)c.push(a),c=c.concat(x()),a=b();return d()?t({type:i.keyframes,name:o,vendor:n,keyframes:c}):l("@keyframes missing '}'")}()||function(){const t=u(),e=/^@media *([^{]+)/.exec(s);if(!e)return;const n=o(g(e)[1]);if(!f())return l("@media missing '{'");const r=x().concat(y());return d()?t({type:i.media,media:n,rules:r}):l("@media missing '}'")}()||function(){const t=u(),e=/^@custom-media\s+(--\S+)\s+([^{;\s][^{;]*);/.exec(s);if(!e)return;const n=g(e);return t({type:i.customMedia,name:o(n[1]),media:o(n[2])})}()||function(){const t=u(),e=/^@supports *([^{]+)/.exec(s);if(!e)return;const n=o(g(e)[1]);if(!f())return l("@supports missing '{'");const r=x().concat(y());return d()?t({type:i.supports,supports:n,rules:r}):l("@supports missing '}'")}()||j()||O()||E()||function(){const t=u(),e=/^@([-\w]+)?document *([^{]+)/.exec(s);if(!e)return;const n=g(e),r=o(n[1]),c=o(n[2]);if(!f())return l("@document missing '{'");const a=x().concat(y());return d()?t({type:i.document,document:c,vendor:r,rules:a}):l("@document missing '}'")}()||function(){const t=u(),e=/^@page */.exec(s);if(!e)return;g(e);const n=k()||[];if(!f())return l("@page missing '{'");let r=x(),o=v();for(;o;)r.push(o),r=r.concat(x()),o=v();return d()?t({type:i.page,selectors:n,declarations:r}):l("@page missing '}'")}()||function(){const t=u(),e=/^@host\s*/.exec(s);if(!e)return;if(g(e),!f())return l("@host missing '{'");const n=x().concat(y());return d()?t({type:i.host,rules:n}):l("@host missing '}'")}()||function(){const t=u(),e=/^@font-face\s*/.exec(s);if(!e)return;if(g(e),!f())return l("@font-face missing '{'");let n=x(),r=v();for(;r;)n.push(r),n=n.concat(x()),r=v();return d()?t({type:i.fontFace,declarations:n}):l("@font-face missing '}'")}()||function(){const t=u(),e=/^@container *([^{]+)/.exec(s);if(!e)return;const n=o(g(e)[1]);if(!f())return l("@container missing '{'");const r=x().concat(y());return d()?t({type:i.container,container:n,rules:r}):l("@container missing '}'")}()||function(){const t=u(),e=/^@starting-style\s*/.exec(s);if(!e)return;if(g(e),!f())return l("@starting-style missing '{'");const n=x().concat(y());return d()?t({type:i.startingStyle,rules:n}):l("@starting-style missing '}'")}()||function(){const t=u(),e=/^@layer *([^{;@]+)/.exec(s);if(!e)return;const n=o(g(e)[1]);if(!f()){const e=/^[;\s]*/.exec(s);return e&&g(e),t({type:i.layer,layer:n})}const r=x().concat(y());return d()?t({type:i.layer,layer:n,rules:r}):l("@layer missing '}'")}()}function S(){const t=u(),e=k();return e?(x(),t({type:i.rule,selectors:e,declarations:w()||[]})):l("selector missing")}return c(function(){const t=y();return{type:i.stylesheet,stylesheet:{source:a?.source,rules:t,parsingErrors:p}}}())},h=(t,e)=>new a(e||{}).compile(t);var u={parse:m,stringify:h};export{i as CssTypes,u as default,m as parse,h as stringify};
//# sourceMappingURL=adobe-css-tools.mjs.map
