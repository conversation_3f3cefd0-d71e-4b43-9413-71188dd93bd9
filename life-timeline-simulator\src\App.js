import React from 'react';
import { GameProvider } from './contexts/GameContext';
import MainMenu from './components/MainMenu';
import GameFlow from './components/GameFlow';
import GameEnd from './components/GameEnd';
import { useGame } from './contexts/GameContext';
import './App.css';

// Main app wrapper component
const AppContent = () => {
  const { gameStarted, gameComplete } = useGame();

  if (!gameStarted) {
    return <MainMenu />;
  }

  if (gameComplete) {
    return <GameEnd />;
  }

  return <GameFlow />;
};

function App() {
  return (
    <GameProvider>
      <div className="App">
        <AppContent />
      </div>
    </GameProvider>
  );
}

export default App;
