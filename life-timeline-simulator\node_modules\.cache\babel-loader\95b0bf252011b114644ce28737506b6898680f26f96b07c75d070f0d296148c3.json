{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LTS\\\\life-timeline-simulator\\\\src\\\\components\\\\LifeStageDisplay.js\",\n  _s = $RefreshSig$();\n// Life stage display component with animations\n\nimport React, { useState, useEffect } from 'react';\nimport { getStageConfig, getLifeProgress } from '../utils/gameUtils';\nimport { LIFE_STAGES } from '../data/gameData';\nimport '../styles/LifeStageDisplay.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LifeStageDisplay = ({\n  currentStage,\n  isTransitioning = false,\n  onTransitionComplete = () => {}\n}) => {\n  _s();\n  const [animationClass, setAnimationClass] = useState('');\n  const stageConfig = getStageConfig(currentStage);\n  const progress = getLifeProgress(currentStage);\n  useEffect(() => {\n    if (isTransitioning) {\n      setAnimationClass('stage-transition');\n      const timer = setTimeout(() => {\n        setAnimationClass('');\n        onTransitionComplete();\n      }, 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [isTransitioning, onTransitionComplete]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `life-stage-display ${animationClass}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stage-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stage-icon-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stage-icon\",\n          style: {\n            backgroundColor: stageConfig.color\n          },\n          children: stageConfig.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stage-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"stage-name\",\n          children: stageConfig.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"stage-description\",\n          children: stageConfig.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stage-age\",\n          children: [\"Age: \", stageConfig.ageRange]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stage-progress\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-label\",\n        children: \"Life Progress\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-fill\",\n          style: {\n            width: `${progress}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-text\",\n        children: [progress, \"% Complete\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(LifeStageDisplay, \"eavCEZcb4+ninKk/DXyzgwlCwks=\");\n_c = LifeStageDisplay;\nexport default LifeStageDisplay;\nvar _c;\n$RefreshReg$(_c, \"LifeStageDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getStageConfig", "getLifeProgress", "LIFE_STAGES", "jsxDEV", "_jsxDEV", "LifeStageDisplay", "currentStage", "isTransitioning", "onTransitionComplete", "_s", "animationClass", "setAnimationClass", "stageConfig", "progress", "timer", "setTimeout", "clearTimeout", "className", "children", "style", "backgroundColor", "color", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "description", "<PERSON><PERSON><PERSON><PERSON>", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/components/LifeStageDisplay.js"], "sourcesContent": ["// Life stage display component with animations\n\nimport React, { useState, useEffect } from 'react';\nimport { getStageConfig, getLifeProgress } from '../utils/gameUtils';\nimport { LIFE_STAGES } from '../data/gameData';\nimport '../styles/LifeStageDisplay.css';\n\nconst LifeStageDisplay = ({ \n  currentStage, \n  isTransitioning = false,\n  onTransitionComplete = () => {} \n}) => {\n  const [animationClass, setAnimationClass] = useState('');\n  const stageConfig = getStageConfig(currentStage);\n  const progress = getLifeProgress(currentStage);\n\n  useEffect(() => {\n    if (isTransitioning) {\n      setAnimationClass('stage-transition');\n      const timer = setTimeout(() => {\n        setAnimationClass('');\n        onTransitionComplete();\n      }, 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [isTransitioning, onTransitionComplete]);\n\n  return (\n    <div className={`life-stage-display ${animationClass}`}>\n      <div className=\"stage-header\">\n        <div className=\"stage-icon-container\">\n          <div \n            className=\"stage-icon\"\n            style={{ backgroundColor: stageConfig.color }}\n          >\n            {stageConfig.icon}\n          </div>\n        </div>\n        \n        <div className=\"stage-info\">\n          <h2 className=\"stage-name\">{stageConfig.name}</h2>\n          <p className=\"stage-description\">{stageConfig.description}</p>\n          <span className=\"stage-age\">Age: {stageConfig.ageRange}</span>\n        </div>\n      </div>\n\n      <div className=\"stage-progress\">\n        <div className=\"progress-label\">Life Progress</div>\n        <div className=\"progress-bar\">\n          <div \n            className=\"progress-fill\"\n            style={{ width: `${progress}%` }}\n          />\n        </div>\n        <div className=\"progress-text\">{progress}% Complete</div>\n      </div>\n    </div>\n  );\n};\n\nexport default LifeStageDisplay;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACpE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,gBAAgB,GAAGA,CAAC;EACxBC,YAAY;EACZC,eAAe,GAAG,KAAK;EACvBC,oBAAoB,GAAGA,CAAA,KAAM,CAAC;AAChC,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMc,WAAW,GAAGZ,cAAc,CAACM,YAAY,CAAC;EAChD,MAAMO,QAAQ,GAAGZ,eAAe,CAACK,YAAY,CAAC;EAE9CP,SAAS,CAAC,MAAM;IACd,IAAIQ,eAAe,EAAE;MACnBI,iBAAiB,CAAC,kBAAkB,CAAC;MACrC,MAAMG,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BJ,iBAAiB,CAAC,EAAE,CAAC;QACrBH,oBAAoB,CAAC,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMQ,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACP,eAAe,EAAEC,oBAAoB,CAAC,CAAC;EAE3C,oBACEJ,OAAA;IAAKa,SAAS,EAAE,sBAAsBP,cAAc,EAAG;IAAAQ,QAAA,gBACrDd,OAAA;MAAKa,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3Bd,OAAA;QAAKa,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnCd,OAAA;UACEa,SAAS,EAAC,YAAY;UACtBE,KAAK,EAAE;YAAEC,eAAe,EAAER,WAAW,CAACS;UAAM,CAAE;UAAAH,QAAA,EAE7CN,WAAW,CAACU;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtB,OAAA;QAAKa,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBd,OAAA;UAAIa,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEN,WAAW,CAACe;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDtB,OAAA;UAAGa,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAEN,WAAW,CAACgB;QAAW;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DtB,OAAA;UAAMa,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,OAAK,EAACN,WAAW,CAACiB,QAAQ;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtB,OAAA;MAAKa,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7Bd,OAAA;QAAKa,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAa;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnDtB,OAAA;QAAKa,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3Bd,OAAA;UACEa,SAAS,EAAC,eAAe;UACzBE,KAAK,EAAE;YAAEW,KAAK,EAAE,GAAGjB,QAAQ;UAAI;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNtB,OAAA;QAAKa,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAEL,QAAQ,EAAC,YAAU;MAAA;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAnDIJ,gBAAgB;AAAA0B,EAAA,GAAhB1B,gBAAgB;AAqDtB,eAAeA,gBAAgB;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}