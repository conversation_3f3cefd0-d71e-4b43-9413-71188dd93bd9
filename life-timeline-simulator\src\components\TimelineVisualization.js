// Timeline visualization component

import React from 'react';
import { LIFE_STAGES, STAGE_CONFIG } from '../data/gameData';
import '../styles/TimelineVisualization.css';

const TimelineVisualization = ({ 
  currentStage, 
  completedStages = [],
  animated = true 
}) => {
  const stages = Object.values(LIFE_STAGES);
  const currentStageIndex = stages.indexOf(currentStage);

  return (
    <div className={`timeline-visualization ${animated ? 'animated' : ''}`}>
      <div className="timeline-header">
        <h3>Life Journey</h3>
      </div>
      
      <div className="timeline-container">
        <div className="timeline-line" />
        
        {stages.map((stage, index) => {
          const stageConfig = STAGE_CONFIG[stage];
          const isCompleted = completedStages.includes(stage);
          const isCurrent = stage === currentStage;
          const isPast = index < currentStageIndex;
          const isFuture = index > currentStageIndex;
          
          return (
            <div
              key={stage}
              className={`timeline-stage ${
                isCompleted ? 'completed' : ''
              } ${
                isCurrent ? 'current' : ''
              } ${
                isPast ? 'past' : ''
              } ${
                isFuture ? 'future' : ''
              }`}
              style={{ '--stage-color': stageConfig.color }}
            >
              <div className="stage-marker">
                <div className="stage-dot">
                  <span className="stage-emoji">{stageConfig.icon}</span>
                </div>
                {isCurrent && <div className="current-pulse" />}
              </div>
              
              <div className="stage-label">
                <div className="stage-title">{stageConfig.name}</div>
                <div className="stage-age">{stageConfig.ageRange}</div>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="timeline-legend">
        <div className="legend-item">
          <div className="legend-dot completed" />
          <span>Completed</span>
        </div>
        <div className="legend-item">
          <div className="legend-dot current" />
          <span>Current</span>
        </div>
        <div className="legend-item">
          <div className="legend-dot future" />
          <span>Future</span>
        </div>
      </div>
    </div>
  );
};

export default TimelineVisualization;
