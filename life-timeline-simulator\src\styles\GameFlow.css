/* GameFlow component styles */

.game-flow {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.game-header {
  margin-bottom: 32px;
}

.game-content {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 32px;
  max-width: 1400px;
  margin: 0 auto;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.sidebar {
  position: sticky;
  top: 20px;
  height: fit-content;
}

/* Stage Complete Card */
.stage-complete-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  border-radius: 20px;
  padding: 32px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  animation: stageCompleteSlideIn 0.8s ease-out;
}

@keyframes stageCompleteSlideIn {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.complete-header {
  margin-bottom: 32px;
}

.complete-icon {
  font-size: 4em;
  margin-bottom: 16px;
  animation: celebrationBounce 1s ease-out;
}

@keyframes celebrationBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

.complete-title {
  margin: 0 0 16px 0;
  font-size: 2.5em;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.complete-description {
  margin: 0;
  font-size: 1.2em;
  color: #666;
  line-height: 1.5;
}

.stage-summary {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: left;
}

.stage-summary h3 {
  margin: 0 0 20px 0;
  font-size: 1.4em;
  font-weight: 700;
  color: #2c3e50;
  text-align: center;
}

.choices-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.choice-summary-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.choice-summary-title {
  font-size: 1.1em;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.choice-summary-selected {
  font-size: 1em;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 8px;
}

.choice-summary-story {
  font-size: 0.95em;
  color: #666;
  font-style: italic;
  line-height: 1.4;
}

.advance-controls {
  display: flex;
  justify-content: center;
}

.advance-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 16px 32px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.advance-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.advance-button:active {
  transform: translateY(-1px);
}

.advance-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.advance-button:hover::before {
  left: 100%;
}

/* Responsive design */
@media (max-width: 1024px) {
  .game-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .sidebar {
    position: static;
    order: -1;
  }
}

@media (max-width: 768px) {
  .game-flow {
    padding: 16px;
  }
  
  .game-header {
    margin-bottom: 24px;
  }
  
  .game-content {
    gap: 20px;
  }
  
  .main-content {
    gap: 20px;
  }
  
  .stage-complete-card {
    padding: 24px;
  }
  
  .complete-icon {
    font-size: 3em;
  }
  
  .complete-title {
    font-size: 2em;
  }
  
  .complete-description {
    font-size: 1.1em;
  }
  
  .stage-summary {
    padding: 20px;
  }
  
  .advance-button {
    padding: 14px 28px;
    font-size: 1em;
  }
}

@media (max-width: 480px) {
  .game-flow {
    padding: 12px;
  }
  
  .stage-complete-card {
    padding: 20px;
    border-radius: 16px;
  }
  
  .complete-icon {
    font-size: 2.5em;
  }
  
  .complete-title {
    font-size: 1.8em;
  }
  
  .complete-description {
    font-size: 1em;
  }
  
  .stage-summary {
    padding: 16px;
  }
  
  .choice-summary-item {
    padding: 12px;
  }
  
  .choice-summary-title {
    font-size: 1em;
  }
  
  .choice-summary-selected {
    font-size: 0.95em;
  }
  
  .choice-summary-story {
    font-size: 0.9em;
  }
  
  .advance-button {
    padding: 12px 24px;
    font-size: 0.95em;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .stage-complete-card {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .complete-title {
    color: #ecf0f1;
  }
  
  .complete-description {
    color: #bdc3c7;
  }
  
  .stage-summary {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .stage-summary h3 {
    color: #ecf0f1;
  }
  
  .choice-summary-item {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .choice-summary-title {
    color: #ecf0f1;
  }
  
  .choice-summary-selected {
    color: #a8b8f0;
  }
  
  .choice-summary-story {
    color: #bdc3c7;
  }
}
