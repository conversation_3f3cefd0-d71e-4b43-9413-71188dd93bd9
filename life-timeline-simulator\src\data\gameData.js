// Game constants and data structures for Life Timeline Simulator

export const LIFE_STAGES = {
  CHILDHOOD: 'childhood',
  TEEN: 'teen',
  YOUNG_ADULT: 'young_adult',
  ADULT: 'adult',
  OLD_AGE: 'old_age'
};

export const STATS = {
  HEALTH: 'health',
  HAPPINESS: 'happiness',
  MONEY: 'money',
  RELATIONSHIPS: 'relationships'
};

// Initial stats for new game
export const INITIAL_STATS = {
  [STATS.HEALTH]: 80,
  [STATS.HAPPINESS]: 70,
  [STATS.MONEY]: 20,
  [STATS.RELATIONSHIPS]: 60
};

// Life stage configurations
export const STAGE_CONFIG = {
  [LIFE_STAGES.CHILDHOOD]: {
    name: 'Childhood',
    description: 'The early years of discovery and learning',
    ageRange: '5-12',
    icon: '🧒',
    color: '#FFB6C1'
  },
  [LIFE_STAGES.TEEN]: {
    name: 'Teenage Years',
    description: 'Finding your identity and making first big decisions',
    ageRange: '13-18',
    icon: '🧑‍🎓',
    color: '#87CEEB'
  },
  [LIFE_STAGES.YOUNG_ADULT]: {
    name: 'Young Adult',
    description: 'Building your career and relationships',
    ageRange: '19-30',
    icon: '👨‍💼',
    color: '#98FB98'
  },
  [LIFE_STAGES.ADULT]: {
    name: 'Adult',
    description: 'Establishing your life and making major decisions',
    ageRange: '31-55',
    icon: '👨‍👩‍👧‍👦',
    color: '#DDA0DD'
  },
  [LIFE_STAGES.OLD_AGE]: {
    name: 'Golden Years',
    description: 'Reflecting on life and enjoying the fruits of your labor',
    ageRange: '56+',
    icon: '👴',
    color: '#F0E68C'
  }
};

// Choices for each life stage
export const LIFE_CHOICES = {
  [LIFE_STAGES.CHILDHOOD]: [
    {
      id: 'childhood_1',
      title: 'School vs Play',
      description: 'How do you spend your free time?',
      options: [
        {
          text: 'Focus on studies and homework',
          effects: { health: -5, happiness: -10, money: 0, relationships: -5 },
          story: 'You became a dedicated student, always finishing homework first.'
        },
        {
          text: 'Play with friends every day',
          effects: { health: 10, happiness: 15, money: 0, relationships: 10 },
          story: 'You made many friends and had countless adventures playing outside.'
        },
        {
          text: 'Balance both study and play',
          effects: { health: 5, happiness: 5, money: 0, relationships: 5 },
          story: 'You learned the importance of balance early in life.'
        }
      ]
    },
    {
      id: 'childhood_2',
      title: 'Family Time',
      description: 'Your family wants to spend more time together',
      options: [
        {
          text: 'Spend lots of time with family',
          effects: { health: 5, happiness: 10, money: 0, relationships: 15 },
          story: 'You built strong family bonds that would last a lifetime.'
        },
        {
          text: 'Prefer independence and alone time',
          effects: { health: 0, happiness: -5, money: 0, relationships: -10 },
          story: 'You developed strong independence but missed some family moments.'
        },
        {
          text: 'Join family activities occasionally',
          effects: { health: 2, happiness: 5, money: 0, relationships: 8 },
          story: 'You maintained good family relationships while keeping some independence.'
        }
      ]
    }
  ],

  [LIFE_STAGES.TEEN]: [
    {
      id: 'teen_1',
      title: 'Academic Path',
      description: 'What kind of student are you?',
      options: [
        {
          text: 'Straight-A student, very focused',
          effects: { health: -10, happiness: -5, money: 5, relationships: -5 },
          story: 'You excelled academically but missed some social experiences.'
        },
        {
          text: 'Social butterfly, average grades',
          effects: { health: 5, happiness: 15, money: -5, relationships: 20 },
          story: 'You were popular and well-liked, though grades suffered slightly.'
        },
        {
          text: 'Well-rounded student',
          effects: { health: 0, happiness: 8, money: 2, relationships: 10 },
          story: 'You maintained good grades while enjoying a healthy social life.'
        }
      ]
    },
    {
      id: 'teen_2',
      title: 'First Job',
      description: 'Do you get a part-time job?',
      options: [
        {
          text: 'Work part-time after school',
          effects: { health: -5, happiness: -5, money: 20, relationships: -5 },
          story: 'You learned the value of hard work and earned your first paycheck.'
        },
        {
          text: 'Focus on school and activities',
          effects: { health: 5, happiness: 10, money: -5, relationships: 10 },
          story: 'You enjoyed your teenage years without work pressure.'
        },
        {
          text: 'Volunteer in the community',
          effects: { health: 0, happiness: 15, money: 0, relationships: 15 },
          story: 'You gave back to the community and made meaningful connections.'
        }
      ]
    }
  ],

  [LIFE_STAGES.YOUNG_ADULT]: [
    {
      id: 'young_adult_1',
      title: 'Career Choice',
      description: 'What career path do you choose?',
      options: [
        {
          text: 'High-paying corporate job',
          effects: { health: -10, happiness: -5, money: 30, relationships: -10 },
          story: 'You climbed the corporate ladder quickly but worked long hours.'
        },
        {
          text: 'Follow your passion (lower pay)',
          effects: { health: 5, happiness: 25, money: -10, relationships: 5 },
          story: 'You pursued your dreams and found fulfillment in your work.'
        },
        {
          text: 'Stable government job',
          effects: { health: 5, happiness: 10, money: 15, relationships: 5 },
          story: 'You chose security and work-life balance over high earnings.'
        }
      ]
    },
    {
      id: 'young_adult_2',
      title: 'Relationships',
      description: 'How do you approach romantic relationships?',
      options: [
        {
          text: 'Focus on career, date casually',
          effects: { health: 0, happiness: 5, money: 10, relationships: -5 },
          story: 'You prioritized your career and kept relationships light.'
        },
        {
          text: 'Seek serious long-term relationship',
          effects: { health: 10, happiness: 15, money: -5, relationships: 25 },
          story: 'You found love and built a strong partnership.'
        },
        {
          text: 'Stay single and independent',
          effects: { health: 5, happiness: 0, money: 5, relationships: -10 },
          story: 'You enjoyed your independence and focused on personal growth.'
        }
      ]
    }
  ],

  [LIFE_STAGES.ADULT]: [
    {
      id: 'adult_1',
      title: 'Family Planning',
      description: 'Do you want to start a family?',
      options: [
        {
          text: 'Have children and focus on family',
          effects: { health: -5, happiness: 20, money: -20, relationships: 20 },
          story: 'You became a parent and found joy in raising children.'
        },
        {
          text: 'Focus on career advancement',
          effects: { health: -10, happiness: 5, money: 25, relationships: -5 },
          story: 'You climbed to executive levels and achieved financial success.'
        },
        {
          text: 'Travel and enjoy life experiences',
          effects: { health: 10, happiness: 15, money: -10, relationships: 10 },
          story: 'You explored the world and collected amazing memories.'
        }
      ]
    },
    {
      id: 'adult_2',
      title: 'Financial Decisions',
      description: 'How do you manage your money?',
      options: [
        {
          text: 'Save and invest aggressively',
          effects: { health: 0, happiness: -5, money: 30, relationships: -5 },
          story: 'You built substantial wealth through careful planning and investing.'
        },
        {
          text: 'Spend on experiences and enjoyment',
          effects: { health: 5, happiness: 20, money: -15, relationships: 10 },
          story: 'You lived life to the fullest and created wonderful memories.'
        },
        {
          text: 'Balance saving and spending',
          effects: { health: 2, happiness: 10, money: 10, relationships: 5 },
          story: 'You found a healthy balance between enjoying life and planning for the future.'
        }
      ]
    }
  ],

  [LIFE_STAGES.OLD_AGE]: [
    {
      id: 'old_age_1',
      title: 'Retirement Lifestyle',
      description: 'How do you spend your golden years?',
      options: [
        {
          text: 'Stay active and pursue hobbies',
          effects: { health: 15, happiness: 20, money: -5, relationships: 10 },
          story: 'You stayed vibrant and discovered new passions in retirement.'
        },
        {
          text: 'Relax and take it easy',
          effects: { health: -5, happiness: 10, money: 5, relationships: 0 },
          story: 'You enjoyed a peaceful and restful retirement.'
        },
        {
          text: 'Volunteer and help others',
          effects: { health: 5, happiness: 25, money: 0, relationships: 20 },
          story: 'You found purpose in giving back to your community.'
        }
      ]
    }
  ]
};

// Stat limits
export const STAT_LIMITS = {
  MIN: 0,
  MAX: 100
};
