{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LTS\\\\life-timeline-simulator\\\\src\\\\components\\\\TimelineVisualization.js\";\n// Timeline visualization component\n\nimport React from 'react';\nimport { LIFE_STAGES, STAGE_CONFIG } from '../data/gameData';\nimport '../styles/TimelineVisualization.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TimelineVisualization = ({\n  currentStage,\n  completedStages = [],\n  animated = true\n}) => {\n  const stages = Object.values(LIFE_STAGES);\n  const currentStageIndex = stages.indexOf(currentStage);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `timeline-visualization ${animated ? 'animated' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timeline-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Life Journey\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timeline-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"timeline-line\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), stages.map((stage, index) => {\n        const stageConfig = STAGE_CONFIG[stage];\n        const isCompleted = completedStages.includes(stage);\n        const isCurrent = stage === currentStage;\n        const isPast = index < currentStageIndex;\n        const isFuture = index > currentStageIndex;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `timeline-stage ${isCompleted ? 'completed' : ''} ${isCurrent ? 'current' : ''} ${isPast ? 'past' : ''} ${isFuture ? 'future' : ''}`,\n          style: {\n            '--stage-color': stageConfig.color\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stage-marker\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stage-dot\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stage-emoji\",\n                children: stageConfig.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 17\n            }, this), isCurrent && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"current-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stage-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stage-title\",\n              children: stageConfig.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stage-age\",\n              children: stageConfig.ageRange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this)]\n        }, stage, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timeline-legend\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"legend-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-dot completed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Completed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"legend-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-dot current\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Current\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"legend-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-dot future\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Future\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_c = TimelineVisualization;\nexport default TimelineVisualization;\nvar _c;\n$RefreshReg$(_c, \"TimelineVisualization\");", "map": {"version": 3, "names": ["React", "LIFE_STAGES", "STAGE_CONFIG", "jsxDEV", "_jsxDEV", "TimelineVisualization", "currentStage", "completedStages", "animated", "stages", "Object", "values", "currentStageIndex", "indexOf", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "stage", "index", "stageConfig", "isCompleted", "includes", "isCurrent", "isPast", "isFuture", "style", "color", "icon", "name", "<PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/components/TimelineVisualization.js"], "sourcesContent": ["// Timeline visualization component\n\nimport React from 'react';\nimport { LIFE_STAGES, STAGE_CONFIG } from '../data/gameData';\nimport '../styles/TimelineVisualization.css';\n\nconst TimelineVisualization = ({ \n  currentStage, \n  completedStages = [],\n  animated = true \n}) => {\n  const stages = Object.values(LIFE_STAGES);\n  const currentStageIndex = stages.indexOf(currentStage);\n\n  return (\n    <div className={`timeline-visualization ${animated ? 'animated' : ''}`}>\n      <div className=\"timeline-header\">\n        <h3>Life Journey</h3>\n      </div>\n      \n      <div className=\"timeline-container\">\n        <div className=\"timeline-line\" />\n        \n        {stages.map((stage, index) => {\n          const stageConfig = STAGE_CONFIG[stage];\n          const isCompleted = completedStages.includes(stage);\n          const isCurrent = stage === currentStage;\n          const isPast = index < currentStageIndex;\n          const isFuture = index > currentStageIndex;\n          \n          return (\n            <div\n              key={stage}\n              className={`timeline-stage ${\n                isCompleted ? 'completed' : ''\n              } ${\n                isCurrent ? 'current' : ''\n              } ${\n                isPast ? 'past' : ''\n              } ${\n                isFuture ? 'future' : ''\n              }`}\n              style={{ '--stage-color': stageConfig.color }}\n            >\n              <div className=\"stage-marker\">\n                <div className=\"stage-dot\">\n                  <span className=\"stage-emoji\">{stageConfig.icon}</span>\n                </div>\n                {isCurrent && <div className=\"current-pulse\" />}\n              </div>\n              \n              <div className=\"stage-label\">\n                <div className=\"stage-title\">{stageConfig.name}</div>\n                <div className=\"stage-age\">{stageConfig.ageRange}</div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n      \n      <div className=\"timeline-legend\">\n        <div className=\"legend-item\">\n          <div className=\"legend-dot completed\" />\n          <span>Completed</span>\n        </div>\n        <div className=\"legend-item\">\n          <div className=\"legend-dot current\" />\n          <span>Current</span>\n        </div>\n        <div className=\"legend-item\">\n          <div className=\"legend-dot future\" />\n          <span>Future</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TimelineVisualization;\n"], "mappings": ";AAAA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,YAAY,QAAQ,kBAAkB;AAC5D,OAAO,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,YAAY;EACZC,eAAe,GAAG,EAAE;EACpBC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,MAAMC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACV,WAAW,CAAC;EACzC,MAAMW,iBAAiB,GAAGH,MAAM,CAACI,OAAO,CAACP,YAAY,CAAC;EAEtD,oBACEF,OAAA;IAAKU,SAAS,EAAE,0BAA0BN,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;IAAAO,QAAA,gBACrEX,OAAA;MAAKU,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BX,OAAA;QAAAW,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAENf,OAAA;MAAKU,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCX,OAAA;QAAKU,SAAS,EAAC;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEhCV,MAAM,CAACW,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAC5B,MAAMC,WAAW,GAAGrB,YAAY,CAACmB,KAAK,CAAC;QACvC,MAAMG,WAAW,GAAGjB,eAAe,CAACkB,QAAQ,CAACJ,KAAK,CAAC;QACnD,MAAMK,SAAS,GAAGL,KAAK,KAAKf,YAAY;QACxC,MAAMqB,MAAM,GAAGL,KAAK,GAAGV,iBAAiB;QACxC,MAAMgB,QAAQ,GAAGN,KAAK,GAAGV,iBAAiB;QAE1C,oBACER,OAAA;UAEEU,SAAS,EAAE,kBACTU,WAAW,GAAG,WAAW,GAAG,EAAE,IAE9BE,SAAS,GAAG,SAAS,GAAG,EAAE,IAE1BC,MAAM,GAAG,MAAM,GAAG,EAAE,IAEpBC,QAAQ,GAAG,QAAQ,GAAG,EAAE,EACvB;UACHC,KAAK,EAAE;YAAE,eAAe,EAAEN,WAAW,CAACO;UAAM,CAAE;UAAAf,QAAA,gBAE9CX,OAAA;YAAKU,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BX,OAAA;cAAKU,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxBX,OAAA;gBAAMU,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEQ,WAAW,CAACQ;cAAI;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,EACLO,SAAS,iBAAItB,OAAA;cAAKU,SAAS,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAENf,OAAA;YAAKU,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BX,OAAA;cAAKU,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEQ,WAAW,CAACS;YAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDf,OAAA;cAAKU,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEQ,WAAW,CAACU;YAAQ;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA,GAtBDE,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBP,CAAC;MAEV,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENf,OAAA;MAAKU,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BX,OAAA;QAAKU,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BX,OAAA;UAAKU,SAAS,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxCf,OAAA;UAAAW,QAAA,EAAM;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACNf,OAAA;QAAKU,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BX,OAAA;UAAKU,SAAS,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtCf,OAAA;UAAAW,QAAA,EAAM;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eACNf,OAAA;QAAKU,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BX,OAAA;UAAKU,SAAS,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCf,OAAA;UAAAW,QAAA,EAAM;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACe,EAAA,GAtEI7B,qBAAqB;AAwE3B,eAAeA,qBAAqB;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}