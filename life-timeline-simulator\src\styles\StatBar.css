/* StatBar component styles */

.stat-bar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-bar:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-bar.animated {
  animation: statBarEnter 0.6s ease-out;
}

@keyframes statBarEnter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-icon {
  font-size: 1.2em;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.stat-name {
  font-weight: 600;
  color: #333;
  font-size: 0.95em;
}

.stat-value-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-value {
  font-weight: 700;
  font-size: 1.1em;
  color: #2c3e50;
}

.stat-change {
  font-size: 0.85em;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 6px;
  animation: changeAppear 0.5s ease-out;
}

.stat-change.positive {
  color: #27ae60;
  background: rgba(39, 174, 96, 0.1);
}

.stat-change.negative {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
}

@keyframes changeAppear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.stat-bar-container {
  position: relative;
  height: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
  overflow: hidden;
}

.stat-bar-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.stat-bar-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 4px;
  background: linear-gradient(90deg, currentColor, currentColor 70%, rgba(255, 255, 255, 0.3));
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-description {
  font-size: 0.8em;
  color: #666;
  font-style: italic;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .stat-bar {
    padding: 12px;
    margin-bottom: 8px;
  }
  
  .stat-header {
    margin-bottom: 8px;
  }
  
  .stat-name {
    font-size: 0.9em;
  }
  
  .stat-value {
    font-size: 1em;
  }
  
  .stat-description {
    font-size: 0.75em;
  }
}

@media (max-width: 480px) {
  .stat-bar {
    padding: 10px;
  }
  
  .stat-info {
    gap: 6px;
  }
  
  .stat-icon {
    font-size: 1.1em;
  }
  
  .stat-name {
    font-size: 0.85em;
  }
}
