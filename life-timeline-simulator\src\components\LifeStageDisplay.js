// Life stage display component with animations

import React, { useState, useEffect } from 'react';
import { getStageConfig, getLifeProgress } from '../utils/gameUtils';
import { LIFE_STAGES } from '../data/gameData';
import '../styles/LifeStageDisplay.css';

const LifeStageDisplay = ({ 
  currentStage, 
  isTransitioning = false,
  onTransitionComplete = () => {} 
}) => {
  const [animationClass, setAnimationClass] = useState('');
  const stageConfig = getStageConfig(currentStage);
  const progress = getLifeProgress(currentStage);

  useEffect(() => {
    if (isTransitioning) {
      setAnimationClass('stage-transition');
      const timer = setTimeout(() => {
        setAnimationClass('');
        onTransitionComplete();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isTransitioning, onTransitionComplete]);

  return (
    <div className={`life-stage-display ${animationClass}`}>
      <div className="stage-header">
        <div className="stage-icon-container">
          <div 
            className="stage-icon"
            style={{ backgroundColor: stageConfig.color }}
          >
            {stageConfig.icon}
          </div>
        </div>
        
        <div className="stage-info">
          <h2 className="stage-name">{stageConfig.name}</h2>
          <p className="stage-description">{stageConfig.description}</p>
          <span className="stage-age">Age: {stageConfig.ageRange}</span>
        </div>
      </div>

      <div className="stage-progress">
        <div className="progress-label">Life Progress</div>
        <div className="progress-bar">
          <div 
            className="progress-fill"
            style={{ width: `${progress}%` }}
          />
        </div>
        <div className="progress-text">{progress}% Complete</div>
      </div>
    </div>
  );
};

export default LifeStageDisplay;
