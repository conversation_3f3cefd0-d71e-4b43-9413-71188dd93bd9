// Game end component with life summary

import React from 'react';
import { useGame } from '../contexts/GameContext';
import StatsDashboard from './StatsDashboard';
import { calculateLifeScore, getLifeEvaluation } from '../utils/gameUtils';
import '../styles/GameEnd.css';

const GameEnd = () => {
  const { stats, getLifeStory, restartGame } = useGame();
  
  const lifeScore = calculateLifeScore(stats);
  const evaluation = getLifeEvaluation(lifeScore);
  const lifeStory = getLifeStory();

  const handleRestart = () => {
    restartGame();
  };

  const handleShare = () => {
    const shareText = `I just completed my Life Timeline Simulation! Final score: ${lifeScore}/100 - ${evaluation.title}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Life Timeline Simulator',
        text: shareText,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(shareText);
      alert('Results copied to clipboard!');
    }
  };

  return (
    <div className="game-end">
      <div className="end-background">
        <div className="celebration-particles">
          {[...Array(20)].map((_, i) => (
            <div key={i} className="particle" style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}>✨</div>
          ))}
        </div>
      </div>

      <div className="end-content">
        <div className="end-header">
          <div className="end-icon">🏁</div>
          <h1 className="end-title">Your Life Journey Complete</h1>
          <p className="end-subtitle">
            You've lived a full life and made countless decisions. Here's your story:
          </p>
        </div>

        <div className="life-evaluation">
          <div className="evaluation-score">
            <div className="score-circle">
              <div className="score-number">{lifeScore}</div>
              <div className="score-label">Life Score</div>
            </div>
          </div>
          
          <div className="evaluation-result">
            <h2 className="evaluation-title">{evaluation.title}</h2>
            <p className="evaluation-description">{evaluation.description}</p>
          </div>
        </div>

        <div className="final-stats">
          <StatsDashboard 
            stats={stats}
            compact={false}
          />
        </div>

        <div className="life-story-section">
          <h3 className="story-title">Your Life Story</h3>
          <div className="story-timeline">
            {lifeStory.map((chapter, index) => (
              <div key={index} className="story-chapter">
                <div className="chapter-stage">{formatStageName(chapter.stage)}</div>
                <div className="chapter-choice">"{chapter.choice}"</div>
                <div className="chapter-story">{chapter.story}</div>
              </div>
            ))}
          </div>
        </div>

        <div className="end-actions">
          <button 
            className="action-button primary"
            onClick={handleRestart}
          >
            <div className="button-icon">🔄</div>
            <div className="button-text">Live Another Life</div>
          </button>

          <button 
            className="action-button secondary"
            onClick={handleShare}
          >
            <div className="button-icon">📤</div>
            <div className="button-text">Share Results</div>
          </button>
        </div>

        <div className="achievements">
          <h4>Life Achievements</h4>
          <div className="achievement-list">
            {getAchievements(stats, lifeScore).map((achievement, index) => (
              <div key={index} className="achievement-item">
                <span className="achievement-icon">{achievement.icon}</span>
                <span className="achievement-text">{achievement.text}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to format stage names
const formatStageName = (stage) => {
  const names = {
    childhood: 'Childhood',
    teen: 'Teenage Years',
    young_adult: 'Young Adult',
    adult: 'Adult',
    old_age: 'Golden Years'
  };
  return names[stage] || stage;
};

// Helper function to get achievements based on stats
const getAchievements = (stats, score) => {
  const achievements = [];
  
  if (stats.health >= 80) {
    achievements.push({ icon: '💪', text: 'Health Enthusiast - Maintained excellent health' });
  }
  
  if (stats.happiness >= 80) {
    achievements.push({ icon: '😊', text: 'Joy Seeker - Lived a very happy life' });
  }
  
  if (stats.money >= 80) {
    achievements.push({ icon: '💰', text: 'Financial Success - Achieved wealth' });
  }
  
  if (stats.relationships >= 80) {
    achievements.push({ icon: '👥', text: 'Social Butterfly - Built strong relationships' });
  }
  
  if (score >= 90) {
    achievements.push({ icon: '🌟', text: 'Life Master - Exceptional life score' });
  }
  
  if (score >= 80) {
    achievements.push({ icon: '🎉', text: 'Well Lived - Great life score' });
  }
  
  // Balanced life achievement
  const statValues = Object.values(stats);
  const maxStat = Math.max(...statValues);
  const minStat = Math.min(...statValues);
  if (maxStat - minStat <= 20) {
    achievements.push({ icon: '⚖️', text: 'Balanced Life - Maintained harmony in all areas' });
  }
  
  return achievements;
};

export default GameEnd;
