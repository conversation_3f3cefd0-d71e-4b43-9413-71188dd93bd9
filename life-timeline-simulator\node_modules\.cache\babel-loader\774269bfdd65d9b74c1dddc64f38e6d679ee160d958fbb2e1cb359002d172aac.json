{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LTS\\\\life-timeline-simulator\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { GameProvider } from './contexts/GameContext';\nimport MainMenu from './components/MainMenu';\nimport GameFlow from './components/GameFlow';\nimport GameEnd from './components/GameEnd';\nimport { useGame } from './contexts/GameContext';\nimport './styles/App.css';\n\n// Main app wrapper component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContent = () => {\n  _s();\n  const {\n    gameStarted,\n    gameComplete\n  } = useGame();\n  if (!gameStarted) {\n    return /*#__PURE__*/_jsxDEV(MainMenu, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 12\n    }, this);\n  }\n  if (gameComplete) {\n    return /*#__PURE__*/_jsxDEV(GameEnd, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(GameFlow, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 10\n  }, this);\n};\n_s(AppContent, \"alracRyIF3EsUMQoMz+DN/pm0WU=\", false, function () {\n  return [useGame];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(GameProvider, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "GameProvider", "MainMenu", "GameFlow", "GameEnd", "useGame", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "gameStarted", "gameComplete", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "children", "className", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/LTS/life-timeline-simulator/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { GameProvider } from './contexts/GameContext';\nimport MainMenu from './components/MainMenu';\nimport GameFlow from './components/GameFlow';\nimport GameEnd from './components/GameEnd';\nimport { useGame } from './contexts/GameContext';\nimport './styles/App.css';\n\n// Main app wrapper component\nconst AppContent = () => {\n  const { gameStarted, gameComplete } = useGame();\n\n  if (!gameStarted) {\n    return <MainMenu />;\n  }\n\n  if (gameComplete) {\n    return <GameEnd />;\n  }\n\n  return <GameFlow />;\n};\n\nfunction App() {\n  return (\n    <GameProvider>\n      <div className=\"App\">\n        <AppContent />\n      </div>\n    </GameProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,kBAAkB;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,WAAW;IAAEC;EAAa,CAAC,GAAGN,OAAO,CAAC,CAAC;EAE/C,IAAI,CAACK,WAAW,EAAE;IAChB,oBAAOH,OAAA,CAACL,QAAQ;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrB;EAEA,IAAIJ,YAAY,EAAE;IAChB,oBAAOJ,OAAA,CAACH,OAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpB;EAEA,oBAAOR,OAAA,CAACJ,QAAQ;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACrB,CAAC;AAACN,EAAA,CAZID,UAAU;EAAA,QACwBH,OAAO;AAAA;AAAAW,EAAA,GADzCR,UAAU;AAchB,SAASS,GAAGA,CAAA,EAAG;EACb,oBACEV,OAAA,CAACN,YAAY;IAAAiB,QAAA,eACXX,OAAA;MAAKY,SAAS,EAAC,KAAK;MAAAD,QAAA,eAClBX,OAAA,CAACC,UAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEnB;AAACK,GAAA,GARQH,GAAG;AAUZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}