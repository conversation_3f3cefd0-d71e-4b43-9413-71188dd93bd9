// Main game flow component

import React, { useState, useEffect } from 'react';
import { useGame } from '../contexts/GameContext';
import LifeStageDisplay from './LifeStageDisplay';
import TimelineVisualization from './TimelineVisualization';
import StatsDashboard from './StatsDashboard';
import ChoicePresentation from './ChoicePresentation';
import { LIFE_STAGES } from '../data/gameData';
import '../styles/GameFlow.css';

const GameFlow = () => {
  const {
    currentStage,
    stats,
    choiceHistory,
    gameComplete,
    getCurrentChoice,
    hasMoreChoicesInStage,
    canAdvanceStage,
    makeChoice,
    advanceStage,
    isStageComplete
  } = useGame();

  const [isTransitioning, setIsTransitioning] = useState(false);
  const [previousStats, setPreviousStats] = useState(null);
  const [showStageComplete, setShowStageComplete] = useState(false);

  const currentChoice = getCurrentChoice();
  const completedStages = getCompletedStages();

  // Handle choice selection
  const handleChoiceSelect = (choice, option) => {
    setPreviousStats({ ...stats });
    makeChoice(choice, option);
    
    // Check if stage is complete after this choice
    setTimeout(() => {
      if (!hasMoreChoicesInStage()) {
        setShowStageComplete(true);
      }
    }, 1000);
  };

  // Handle stage advancement
  const handleAdvanceStage = () => {
    setIsTransitioning(true);
    setShowStageComplete(false);
    
    setTimeout(() => {
      advanceStage();
      setIsTransitioning(false);
      setPreviousStats(null);
    }, 1000);
  };

  // Get completed stages based on choice history
  function getCompletedStages() {
    const stages = Object.values(LIFE_STAGES);
    const currentIndex = stages.indexOf(currentStage);
    return stages.slice(0, currentIndex);
  }

  // Get stage summary for completed stage
  const getStageChoices = () => {
    return choiceHistory.filter(choice => choice.stage === currentStage);
  };

  if (gameComplete) {
    return null; // Game end component will be handled separately
  }

  return (
    <div className="game-flow">
      <div className="game-header">
        <TimelineVisualization 
          currentStage={currentStage}
          completedStages={completedStages}
          animated={true}
        />
      </div>

      <div className="game-content">
        <div className="main-content">
          <LifeStageDisplay 
            currentStage={currentStage}
            isTransitioning={isTransitioning}
            onTransitionComplete={() => setIsTransitioning(false)}
          />

          {currentChoice && !showStageComplete && (
            <ChoicePresentation
              choice={currentChoice}
              onChoiceSelect={handleChoiceSelect}
              showEffects={true}
            />
          )}

          {showStageComplete && (
            <StageCompleteCard 
              stage={currentStage}
              choices={getStageChoices()}
              onAdvance={handleAdvanceStage}
              canAdvance={canAdvanceStage}
            />
          )}
        </div>

        <div className="sidebar">
          <StatsDashboard 
            stats={stats}
            previousStats={previousStats}
            showChanges={previousStats !== null}
            compact={false}
          />
        </div>
      </div>
    </div>
  );
};

// Stage complete card component
const StageCompleteCard = ({ stage, choices, onAdvance, canAdvance }) => {
  return (
    <div className="stage-complete-card">
      <div className="complete-header">
        <div className="complete-icon">🎉</div>
        <h2 className="complete-title">Stage Complete!</h2>
        <p className="complete-description">
          You've finished this chapter of your life. Here's what happened:
        </p>
      </div>

      <div className="stage-summary">
        <h3>Your Choices:</h3>
        <div className="choices-summary">
          {choices.map((choice, index) => (
            <div key={index} className="choice-summary-item">
              <div className="choice-summary-title">{choice.choiceTitle}</div>
              <div className="choice-summary-selected">{choice.selectedOption}</div>
              <div className="choice-summary-story">{choice.story}</div>
            </div>
          ))}
        </div>
      </div>

      {canAdvance && (
        <div className="advance-controls">
          <button 
            className="advance-button"
            onClick={onAdvance}
          >
            Continue to Next Stage
          </button>
        </div>
      )}
    </div>
  );
};

export default GameFlow;
