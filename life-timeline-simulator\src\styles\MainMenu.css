/* MainMenu component styles */

.main-menu {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.menu-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.floating-icons {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-icon {
  position: absolute;
  font-size: 3em;
  opacity: 0.1;
  animation: floatIcon 6s ease-in-out infinite;
  filter: blur(1px);
}

.floating-icon:nth-child(1) {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.floating-icon:nth-child(2) {
  top: 20%;
  right: 15%;
  animation-delay: 1.2s;
}

.floating-icon:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation-delay: 2.4s;
}

.floating-icon:nth-child(4) {
  bottom: 20%;
  right: 10%;
  animation-delay: 3.6s;
}

.floating-icon:nth-child(5) {
  top: 50%;
  left: 50%;
  animation-delay: 4.8s;
}

@keyframes floatIcon {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(5deg);
  }
  66% {
    transform: translateY(10px) rotate(-3deg);
  }
}

.menu-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 600px;
  width: 100%;
  animation: menuSlideIn 1s ease-out;
}

@keyframes menuSlideIn {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.game-title {
  margin-bottom: 48px;
}

.title-main {
  font-size: 3.5em;
  font-weight: 800;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  line-height: 1.1;
}

.title-subtitle {
  font-size: 1.3em;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.4;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.menu-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 48px;
}

.menu-button {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px 32px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.menu-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.25);
}

.menu-button.primary {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-color: rgba(255, 255, 255, 0.3);
}

.menu-button.primary:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
}

.menu-button.secondary {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
}

.menu-button.secondary:hover {
  background: rgba(102, 126, 234, 0.3);
}

.menu-button.tertiary {
  background: rgba(118, 75, 162, 0.2);
  border-color: rgba(118, 75, 162, 0.3);
}

.menu-button.tertiary:hover {
  background: rgba(118, 75, 162, 0.3);
}

.button-icon {
  font-size: 2em;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.button-text {
  text-align: left;
  flex: 1;
}

.button-title {
  font-size: 1.3em;
  font-weight: 700;
  margin-bottom: 4px;
}

.button-description {
  font-size: 0.95em;
  opacity: 0.8;
  font-weight: 400;
}

.menu-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.menu-button:hover::before {
  left: 100%;
}

.game-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 32px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.feature-icon {
  font-size: 1.5em;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.feature-text {
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.menu-footer {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85em;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .main-menu {
    padding: 32px 16px;
  }
  
  .title-main {
    font-size: 2.8em;
  }
  
  .title-subtitle {
    font-size: 1.1em;
  }
  
  .menu-buttons {
    gap: 16px;
    margin-bottom: 32px;
  }
  
  .menu-button {
    padding: 16px 24px;
    gap: 16px;
  }
  
  .button-icon {
    font-size: 1.8em;
  }
  
  .button-title {
    font-size: 1.2em;
  }
  
  .button-description {
    font-size: 0.9em;
  }
  
  .game-features {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .feature-item {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .main-menu {
    padding: 24px 12px;
  }
  
  .title-main {
    font-size: 2.2em;
  }
  
  .title-subtitle {
    font-size: 1em;
  }
  
  .menu-button {
    padding: 14px 20px;
    gap: 12px;
    flex-direction: column;
    text-align: center;
  }
  
  .button-text {
    text-align: center;
  }
  
  .button-icon {
    font-size: 1.6em;
  }
  
  .button-title {
    font-size: 1.1em;
  }
  
  .button-description {
    font-size: 0.85em;
  }
  
  .feature-item {
    padding: 10px;
    gap: 10px;
  }
  
  .feature-icon {
    font-size: 1.3em;
  }
  
  .feature-text {
    font-size: 0.85em;
  }
  
  .menu-footer {
    font-size: 0.8em;
    bottom: 16px;
  }
}
