// Utility functions for game mechanics

import { STAT_LIMITS, LIFE_STAGES, STAGE_CONFIG } from '../data/gameData';

/**
 * Apply stat changes while respecting min/max limits
 * @param {Object} currentStats - Current player stats
 * @param {Object} effects - Stat changes to apply
 * @returns {Object} Updated stats
 */
export const applyStatEffects = (currentStats, effects) => {
  const newStats = { ...currentStats };
  
  Object.keys(effects).forEach(stat => {
    newStats[stat] = Math.max(
      STAT_LIMITS.MIN,
      Math.min(STAT_LIMITS.MAX, newStats[stat] + effects[stat])
    );
  });
  
  return newStats;
};

/**
 * Get the next life stage
 * @param {string} currentStage - Current life stage
 * @returns {string|null} Next stage or null if at the end
 */
export const getNextStage = (currentStage) => {
  const stages = Object.values(LIFE_STAGES);
  const currentIndex = stages.indexOf(currentStage);
  
  if (currentIndex === -1 || currentIndex === stages.length - 1) {
    return null;
  }
  
  return stages[currentIndex + 1];
};

/**
 * Check if the game is complete
 * @param {string} currentStage - Current life stage
 * @returns {boolean} True if game is complete
 */
export const isGameComplete = (currentStage) => {
  return currentStage === LIFE_STAGES.OLD_AGE;
};

/**
 * Calculate overall life score based on final stats
 * @param {Object} stats - Final player stats
 * @returns {number} Overall score (0-100)
 */
export const calculateLifeScore = (stats) => {
  const totalStats = Object.values(stats).reduce((sum, stat) => sum + stat, 0);
  return Math.round(totalStats / Object.keys(stats).length);
};

/**
 * Get life evaluation based on score
 * @param {number} score - Life score (0-100)
 * @returns {Object} Evaluation with title and description
 */
export const getLifeEvaluation = (score) => {
  if (score >= 90) {
    return {
      title: "Exceptional Life! 🌟",
      description: "You lived an extraordinary life, excelling in all areas and finding true fulfillment."
    };
  } else if (score >= 80) {
    return {
      title: "Great Life! 🎉",
      description: "You lived a wonderful life with strong relationships, good health, and happiness."
    };
  } else if (score >= 70) {
    return {
      title: "Good Life 😊",
      description: "You lived a solid life with some great moments and meaningful experiences."
    };
  } else if (score >= 60) {
    return {
      title: "Average Life 😐",
      description: "You lived a decent life with both ups and downs, learning from your experiences."
    };
  } else if (score >= 50) {
    return {
      title: "Challenging Life 😔",
      description: "Life had its difficulties, but you persevered through tough times."
    };
  } else {
    return {
      title: "Difficult Life 😢",
      description: "Life was quite challenging, but every experience taught you something valuable."
    };
  }
};

/**
 * Get stat color based on value
 * @param {number} value - Stat value (0-100)
 * @returns {string} CSS color
 */
export const getStatColor = (value) => {
  if (value >= 80) return '#4CAF50'; // Green
  if (value >= 60) return '#8BC34A'; // Light Green
  if (value >= 40) return '#FFC107'; // Yellow
  if (value >= 20) return '#FF9800'; // Orange
  return '#F44336'; // Red
};

/**
 * Get stat icon based on stat type
 * @param {string} statType - Type of stat
 * @returns {string} Emoji icon
 */
export const getStatIcon = (statType) => {
  const icons = {
    health: '❤️',
    happiness: '😊',
    money: '💰',
    relationships: '👥'
  };
  return icons[statType] || '📊';
};

/**
 * Format stat name for display
 * @param {string} statType - Type of stat
 * @returns {string} Formatted name
 */
export const formatStatName = (statType) => {
  return statType.charAt(0).toUpperCase() + statType.slice(1);
};

/**
 * Generate a random choice if user takes too long (optional feature)
 * @param {Array} choices - Available choices
 * @returns {Object} Random choice
 */
export const getRandomChoice = (choices) => {
  const randomIndex = Math.floor(Math.random() * choices.length);
  return choices[randomIndex];
};

/**
 * Save game state to localStorage
 * @param {Object} gameState - Current game state
 */
export const saveGameState = (gameState) => {
  try {
    localStorage.setItem('lts_game_state', JSON.stringify(gameState));
  } catch (error) {
    console.error('Failed to save game state:', error);
  }
};

/**
 * Load game state from localStorage
 * @returns {Object|null} Saved game state or null
 */
export const loadGameState = () => {
  try {
    const savedState = localStorage.getItem('lts_game_state');
    return savedState ? JSON.parse(savedState) : null;
  } catch (error) {
    console.error('Failed to load game state:', error);
    return null;
  }
};

/**
 * Clear saved game state
 */
export const clearGameState = () => {
  try {
    localStorage.removeItem('lts_game_state');
  } catch (error) {
    console.error('Failed to clear game state:', error);
  }
};

/**
 * Get progress percentage through life stages
 * @param {string} currentStage - Current life stage
 * @returns {number} Progress percentage (0-100)
 */
export const getLifeProgress = (currentStage) => {
  const stages = Object.values(LIFE_STAGES);
  const currentIndex = stages.indexOf(currentStage);
  
  if (currentIndex === -1) return 0;
  
  return Math.round(((currentIndex + 1) / stages.length) * 100);
};

/**
 * Get stage configuration
 * @param {string} stage - Life stage
 * @returns {Object} Stage configuration
 */
export const getStageConfig = (stage) => {
  return STAGE_CONFIG[stage] || {};
};
